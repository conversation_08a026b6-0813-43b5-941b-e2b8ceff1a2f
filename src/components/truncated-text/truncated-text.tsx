import { Tooltip, Typography, TypographyProps, SxProps, Theme } from "@mui/material";
import Link from "next/link";

interface TruncatedTextProps {
  text: string;
  isLink?: boolean;
  link?: string;
  openInNewTab?: boolean;
  maxLines?: number;
  typographyProps?: Partial<Omit<TypographyProps, "children" | "onClick" | "sx">> & {
    sx?: SxProps<Theme>;
  };
}

const TruncatedText = ({
  text,
  isLink = false,
  link = "",
  openInNewTab = true,
  maxLines = 1,
  typographyProps = {},
}: TruncatedTextProps) => {
  const defaultSx: SxProps<Theme> = {
    display: "-webkit-box",
    WebkitLineClamp: maxLines,
    WebkitBoxOrient: "vertical",
    overflow: "hidden",
    textOverflow: "ellipsis",
    lineHeight: "1.2em",
    maxHeight: `${maxLines * 1.2}em`,
    wordBreak: "break-word",
    cursor: isLink ? "pointer" : "default",
    ...(isLink && {
      "&:hover": {
        cursor: "pointer",
      },
    }),
  };

  const mergedSx: SxProps<Theme> = {
    ...defaultSx,
    ...(typographyProps.sx || {}),
  };

  const finalTypographyProps: TypographyProps = {
    ...typographyProps,
    sx: mergedSx,
  };

  const renderContent = () => <Typography {...finalTypographyProps}>{text}</Typography>;

  return (
    <Tooltip title={text} placement="top-start" arrow>
      {isLink && link ? (
        <Link
          href={link}
          target={openInNewTab ? "_blank" : "_self"}
          style={{ textDecoration: "none", color: "inherit" }}
        >
          {renderContent()}
        </Link>
      ) : (
        renderContent()
      )}
    </Tooltip>
  );
};

export default TruncatedText;
