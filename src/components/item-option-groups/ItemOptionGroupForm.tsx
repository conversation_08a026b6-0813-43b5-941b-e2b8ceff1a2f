import React, { useEffect, useState } from "react";
import {
  Autocomplete,
  Box,
  Button,
  Card,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  IconButton,
  InputAdornment,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { Controller, FormProvider, Path, useFieldArray, useForm } from "react-hook-form";

import { useDispatch } from "react-redux";

import { AppDispatch } from "@/src/store";
import { useRouter } from "next/router";
import { yupResolver } from "@hookform/resolvers/yup";
import { createBasicCustomerInfoSchema } from "@/src/utils/validations/validationSchema";
import { useTranslation } from "react-i18next";
import * as Yup from "yup";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  AddCircle as AddCircleIcon,
} from "@mui/icons-material";
import { v4 as uuidv4 } from "uuid";
import { useItemOptionGroup } from "@/src/api/hooks/item-option-group/use-item-option-group";
import { CreateItemGroupRequestBodyRequest } from "@/src/api/types/item-option-group.type";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useItemOption } from "@/src/api/hooks/item-option/use-item-option";
import { paths } from "@/src/paths";
import toast from "react-hot-toast";
import { ItemOptionCreateDto } from "@/src/api/services/item-option/item-option.service";

type DefaultValuesType = {
  name?: string;
  require?: boolean;
  isMultiSelect?: boolean;
  options?: {
    itemOptionName?: string;
    itemOptionPrice?: number;
    itemOptionId?: string;
  }[];
};

// const defaultValues: DefaultValuesType = {
//   name: '',
//   require: false,
//   isMultiSelect: false,
//   itemOptionName: '',
//   itemOptionPrice: ''
// };

interface IItemOption {
  itemOptionGroupId: string;
  shopId: string;
  name: string;
  price: string;
}
interface IItemOptionGroup {
  shopId: string;
  name: string;
  require: boolean;
  isMultiSelect: boolean;
}

const validateSchema = Yup.object().shape({
  name: Yup.string()
    .trim()
    .required("Tên nhóm là bắt buộc")
    .max(255, "Tên nhóm không được vượt quá 255 ký tự"),
  require: Yup.boolean(),
  isMultiSelect: Yup.boolean(),
  itemOptionName: Yup.string(),
  itemOptionPrice: Yup.number(),
});

export default function ItemOptionGroupForm() {
  const [loading, setLoading] = useState<boolean>(false);
  const [localSelectedOptionGroups, setLocalSelectedOptionGroups] = useState([]);
  const [options, setOptions] = useState([]);
  const [itemOption, setItemOption] = useState<IItemOption[]>([]);
  const [itemOptionGroup, setItemOptionGroup] = useState<IItemOptionGroup>();
  const storeId = useStoreId();

  const { listItemOptionGroup, createItemOptionGroup } = useItemOptionGroup();
  const { createItemOption, createAndUpdateItemOption } = useItemOption();
  const methods = useForm<DefaultValuesType>({
    resolver: yupResolver(validateSchema),
  });

  const {
    control,
    handleSubmit,
    register,
    setValue,
    formState: { errors },
  } = methods;
  const { fields, append, remove } = useFieldArray({
    control,
    name: "options",
  });
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const onSubmitForm = async (data) => {
    setLoading(true);
    const filteredOptions = data.options.filter((option) => {
      const hasName = option.itemOptionName !== undefined && option.itemOptionName !== "";
      const hasPrice = option.itemOptionPrice !== undefined && option.itemOptionPrice !== "";

      return hasName || hasPrice;
    });

    const hasPartialData = filteredOptions.some((option) => {
      const hasName = option.itemOptionName !== undefined && option.itemOptionName !== "";
      const hasPrice = option.itemOptionPrice !== undefined && option.itemOptionPrice !== "";
      return (hasName && !hasPrice) || (!hasName && hasPrice);
    });
    if (hasPartialData) {
      toast.error("Vui lòng nhập đầy đủ cả tên và giá!");
      return;
    } else {
      const newData = {
        ...data,
        options: filteredOptions,
      };
      const objectItemOptionGroup: CreateItemGroupRequestBodyRequest = {
        shopId: storeId,
        isMultiSelect: newData.isMultiSelect,
        require: newData.require,
        name: newData.name,
      };
      const res = await createItemOptionGroup(objectItemOptionGroup);
      if (res?.status === 200) {
        let newArr: ItemOptionCreateDto[] = [];
        newData?.options?.map((item) => {
          const newObj = {
            shopId: storeId,
            price: Number(item?.itemOptionPrice),
            name: item?.itemOptionName,
            itemOptionGroupId: res?.data?.itemOptionGroupId,
          };
          newArr.push(newObj);
        });
        await createAndUpdateItemOption(storeId, newArr);
        router.push(paths.itemGroups.list);
        setLoading(false);
      }
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const onAddItemOption = () => {
    append({ itemOptionName: "", itemOptionPrice: 0 });
  };

  const removeItemOptionGroup = (index) => {
    remove(index);
  };

  return (
    <>
      <FormProvider {...methods}>
        <Grid container spacing={2}>
          {/* Cột chiếm 8 phần */}
          <Grid size={{ xs: 12, md: 8 }}>
            <Card sx={{ p: 2 }}>
              <Box>
                <Typography variant="subtitle1" gutterBottom sx={{ display: "flex" }}>
                  Tên nhóm tùy chọn{" "}
                  <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
                </Typography>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      error={!!errors.name}
                      helperText={errors.name?.message as string}
                      variant="outlined"
                    />
                  )}
                />
              </Box>
              <Box>
                <Controller
                  name="require"
                  control={control}
                  render={({ field }) => (
                    <div>
                      <FormControlLabel
                        control={<Checkbox {...field} checked={field.value} />}
                        label="Bắt buộc chọn"
                      />
                      {errors.require && (
                        <FormHelperText style={{ color: "red" }}>
                          {errors.require.message}
                        </FormHelperText>
                      )}
                    </div>
                  )}
                />
              </Box>
              <Box>
                <Controller
                  name="isMultiSelect"
                  control={control}
                  render={({ field }) => (
                    <div>
                      <FormControlLabel
                        control={<Checkbox {...field} checked={field.value} />}
                        label="Lựa chọn nhiều tùy chọn"
                      />
                      {errors.isMultiSelect && (
                        <FormHelperText style={{ color: "red" }}>
                          {errors.isMultiSelect.message}
                        </FormHelperText>
                      )}
                    </div>
                  )}
                />
              </Box>
            </Card>
          </Grid>
        </Grid>

        <Stack spacing={2} sx={{ marginTop: 4 }}>
          {fields.map((item, index) => (
            <Stack spacing={1} key={item.id}>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr auto",
                  gap: 1,
                  alignItems: "center",
                }}
              >
                <Box>
                  <Controller
                    name={`options.${index}.itemOptionName` as Path<DefaultValuesType>} // Unique field name
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        required
                        // error={!!errors?.options?.[index]?.itemOptionName}
                        // helperText={errors?.options?.[index]?.itemOptionName?.message}
                        variant="outlined"
                        placeholder="Tên tuỳ chọn"
                      />
                    )}
                  />
                </Box>
                <Box>
                  <Controller
                    name={`options.${index}.itemOptionPrice` as Path<DefaultValuesType>} // Unique field name
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        fullWidth
                        type="number"
                        required
                        slotProps={{
                          input: {
                            endAdornment: <InputAdornment position="end">đ</InputAdornment>,
                          },
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "-" || e.key === "e" || e.key === "E") {
                            e.preventDefault();
                          }
                        }}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (parseFloat(value) < 0 || value === "-") {
                            field.onChange(0); // Đặt lại thành 0 nếu nhập số âm
                          } else {
                            field.onChange(value);
                          }
                        }}
                        onWheel={(e) => e.target instanceof HTMLElement && e.target.blur()}
                        // error={!!errors?.options?.[index]?.itemOptionPrice}
                        // helperText={errors?.options?.[index]?.itemOptionPrice?.message}
                        variant="outlined"
                        placeholder="Giá"
                      />
                    )}
                  />
                </Box>
                <Box sx={{ mt: "2px" }}>
                  <Tooltip title="Xóa" arrow placement="top">
                    <IconButton
                      onClick={() => removeItemOptionGroup(index)}
                      size="small"
                      sx={{
                        bgcolor: "error.lighter",
                        color: "error.main",
                        "&:hover": { bgcolor: "error.light" },
                        width: 32,
                        height: 32,
                      }}
                    >
                      <DeleteIcon fontSize="medium" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>
            </Stack>
          ))}
          <Button
            startIcon={<AddCircleIcon />}
            onClick={onAddItemOption}
            variant="outlined"
            disabled={fields.length >= 10}
            sx={{
              borderStyle: "dashed",
              height: 40,
              color: "#2654FE",
              borderColor: "#2654FE",
              "&:hover": {
                borderStyle: "dashed",
                bgcolor: "primary.lighter",
              },
            }}
          >
            Thêm tùy chọn ({fields.length}/10)
          </Button>
        </Stack>
        <Divider />
        <Grid container sx={{ mt: 3 }}>
          <Box display="flex" gap={1}>
            <Button
              sx={{ color: "#2654FE", borderColor: "#2654FE" }}
              variant="outlined"
              onClick={handleCancel}
            >
              Hủy bỏ
            </Button>
            <Button
              sx={{ background: "#2654FE" }}
              onClick={handleSubmit(onSubmitForm)}
              variant="contained"
              color="primary"
              disabled={loading ? true : false}
            >
              Lưu
            </Button>
          </Box>
        </Grid>
      </FormProvider>
    </>
  );
}
