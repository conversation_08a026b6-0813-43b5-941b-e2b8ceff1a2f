import { useMemo, useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import Box from "@mui/material/Box";
import Drawer from "@mui/material/Drawer";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { useTheme } from "@mui/material/styles";
import { logger } from "@/src/utils/logger";
import { Scrollbar } from "src/components/scrollbar";
import { usePathname } from "src/hooks/use-pathname";
import { TenantSwitch } from "../tenant-switch";
import { SideNavSection } from "./side-nav-section";
import { formatDisplayPhoneNumber } from "@/src/utils/format";
import Collapse from "@mui/material/Collapse";
import { formatDateDisplay } from "@/src/utils/date-utils";
import { useFunction } from "@/src/api/hooks/function/use-function";
import { paths } from "@/src/paths";
// import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions"; // Temporarily unused
import { useAppSelector } from "@/src/redux/hooks";
// import Link from "next/link"; // Temporarily unused
import { useSidebar } from "src/contexts/sidebar-context";
import { SIDE_NAV_WIDTH, SIDE_NAV_COLLAPSED_WIDTH } from "src/components/main-layout";
import TruncatedText from "@/src/components/truncated-text/truncated-text";

const useCssVars = (color) => {
  const theme = useTheme();

  return useMemo(() => {
    switch (color) {
      case "blend-in":
        if (theme.palette.mode === "dark") {
          return {
            "--nav-bg": theme.palette.background.default,
            "--nav-color": theme.palette.neutral[100],
            "--nav-border-color": theme.palette.neutral[700],
            "--nav-logo-border": theme.palette.neutral[700],
            "--nav-section-title-color": theme.palette.neutral[400],
            "--nav-item-color": theme.palette.neutral[400],
            "--nav-item-hover-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-color": theme.palette.text.primary,
            "--nav-item-disabled-color": theme.palette.neutral[600],
            "--nav-item-icon-color": theme.palette.neutral[500],
            "--nav-item-icon-active-color": theme.palette.primary.main,
            "--nav-item-icon-disabled-color": theme.palette.neutral[700],
            "--nav-item-chevron-color": theme.palette.neutral[700],
            "--nav-scrollbar-color": theme.palette.neutral[400],
          };
        } else {
          return {
            "--nav-bg": theme.palette.background.default,
            "--nav-color": theme.palette.text.primary,
            "--nav-border-color": theme.palette.neutral[100],
            "--nav-logo-border": theme.palette.neutral[100],
            "--nav-section-title-color": theme.palette.neutral[400],
            "--nav-item-color": theme.palette.text.secondary,
            "--nav-item-hover-bg": theme.palette.action.hover,
            "--nav-item-active-bg": theme.palette.action.selected,
            "--nav-item-active-color": theme.palette.text.primary,
            "--nav-item-disabled-color": theme.palette.neutral[400],
            "--nav-item-icon-color": theme.palette.neutral[400],
            "--nav-item-icon-active-color": theme.palette.primary.main,
            "--nav-item-icon-disabled-color": theme.palette.neutral[400],
            "--nav-item-chevron-color": theme.palette.neutral[400],
            "--nav-scrollbar-color": theme.palette.neutral[900],
          };
        }

      case "discrete":
        if (theme.palette.mode === "dark") {
          return {
            "--nav-bg": theme.palette.neutral[900],
            "--nav-color": theme.palette.neutral[100],
            "--nav-border-color": theme.palette.neutral[700],
            "--nav-logo-border": theme.palette.neutral[700],
            "--nav-section-title-color": theme.palette.neutral[400],
            "--nav-item-color": theme.palette.neutral[400],
            "--nav-item-hover-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-color": theme.palette.text.primary,
            "--nav-item-disabled-color": theme.palette.neutral[600],
            "--nav-item-icon-color": theme.palette.neutral[500],
            "--nav-item-icon-active-color": theme.palette.primary.main,
            "--nav-item-icon-disabled-color": theme.palette.neutral[700],
            "--nav-item-chevron-color": theme.palette.neutral[700],
            "--nav-scrollbar-color": theme.palette.neutral[400],
          };
        } else {
          return {
            "--nav-bg": theme.palette.neutral[50],
            "--nav-color": theme.palette.text.primary,
            "--nav-border-color": theme.palette.divider,
            "--nav-logo-border": theme.palette.neutral[200],
            "--nav-section-title-color": theme.palette.neutral[500],
            "--nav-item-color": theme.palette.neutral[500],
            "--nav-item-hover-bg": theme.palette.action.hover,
            "--nav-item-active-bg": theme.palette.action.selected,
            "--nav-item-active-color": theme.palette.text.primary,
            "--nav-item-disabled-color": theme.palette.neutral[400],
            "--nav-item-icon-color": theme.palette.neutral[400],
            "--nav-item-icon-active-color": theme.palette.primary.main,
            "--nav-item-icon-disabled-color": theme.palette.neutral[400],
            "--nav-item-chevron-color": theme.palette.neutral[400],
            "--nav-scrollbar-color": theme.palette.neutral[900],
          };
        }

      case "evident":
        if (theme.palette.mode === "dark") {
          return {
            "--nav-bg": theme.palette.neutral[800],
            "--nav-color": theme.palette.common.white,
            "--nav-border-color": "transparent",
            "--nav-logo-border": theme.palette.neutral[700],
            "--nav-section-title-color": theme.palette.neutral[400],
            "--nav-item-color": theme.palette.neutral[400],
            "--nav-item-hover-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-color": theme.palette.common.white,
            "--nav-item-disabled-color": theme.palette.neutral[500],
            "--nav-item-icon-color": theme.palette.neutral[400],
            "--nav-item-icon-active-color": theme.palette.primary.main,
            "--nav-item-icon-disabled-color": theme.palette.neutral[500],
            "--nav-item-chevron-color": theme.palette.neutral[600],
            "--nav-scrollbar-color": theme.palette.neutral[400],
          };
        } else {
          return {
            "--nav-bg": theme.palette.neutral[800],
            "--nav-color": theme.palette.common.white,
            "--nav-border-color": "transparent",
            "--nav-logo-border": theme.palette.neutral[700],
            "--nav-section-title-color": theme.palette.neutral[400],
            "--nav-item-color": theme.palette.neutral[400],
            "--nav-item-hover-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-bg": "rgba(255, 255, 255, 0.04)",
            "--nav-item-active-color": theme.palette.common.white,
            "--nav-item-disabled-color": theme.palette.neutral[500],
            "--nav-item-icon-color": theme.palette.neutral[400],
            "--nav-item-icon-active-color": theme.palette.primary.main,
            "--nav-item-icon-disabled-color": theme.palette.neutral[500],
            "--nav-item-chevron-color": theme.palette.neutral[600],
            "--nav-scrollbar-color": theme.palette.neutral[400],
          };
        }

      default:
        return {};
    }
  }, [theme, color]);
};

export const SideNav = (props) => {
  const { getActivePackageFunctions } = useFunction();
  const [isDetailsVisible, setIsDetailsVisible] = useState(false);
  const { profile } = useAppSelector((state) => state.profile);
  const [activePackage, setActivePackage] = useState([]);
  const hasFetched = useRef(false);
  // const { isAgency } = useAllPermissions(); // Temporarily unused
  const { isCollapsed } = useSidebar();

  const { color = "evident", sections = [] } = props;
  const pathname = usePathname();
  const cssVars = useCssVars(color);

  const limit = 20;
  const searchQuery = null;
  const fetchActivePackageFunctions = async () => {
    if (hasFetched.current) return;
    hasFetched.current = true;
    try {
      const res = await getActivePackageFunctions();
      if (res && res.status === 200) {
        setActivePackage(res.data.data);
      }
    } catch (error) {
      logger.error("Error fetching active package functions:", error);
    }
  };
  useEffect(() => {
    fetchActivePackageFunctions();
  }, []);

  const handleToggleDetails = () => {
    setIsDetailsVisible((prev) => !prev);
  };

  if (!profile) {
    return null;
  }

  // useEffect(() => {
  // }, []);

  return (
    <Drawer
      anchor="left"
      open
      PaperProps={{
        sx: {
          ...cssVars,
          backgroundColor: "#ffffff",
          borderRightColor: "#D9D9D9",
          borderRightStyle: "solid",
          borderRightWidth: 2,
          color: "var(--nav-color)",
          width: isCollapsed ? SIDE_NAV_COLLAPSED_WIDTH : SIDE_NAV_WIDTH,
          transition: "width 0.3s ease",
        },
      }}
      variant="permanent"
    >
      <Box></Box>
      <Scrollbar
        sx={{
          height: "100%",
          "& .simplebar-content": {
            height: "100%",
          },
          "& .simplebar-scrollbar:before": {
            background: "var(--nav-scrollbar-color)",
          },
        }}
      >
        <Stack sx={{ height: "100%" }}>
          <Stack
            alignItems="center"
            direction="row"
            spacing={2}
            sx={{
              p: isCollapsed ? 1 : 3,
              justifyContent: isCollapsed ? "center" : "flex-start",
              mb: isCollapsed ? 2 : 0,
            }}
          >
            <TenantSwitch sx={{ flexGrow: isCollapsed ? 0 : 1 }} isCollapsed={isCollapsed} />
          </Stack>
          <Stack
            component="nav"
            spacing={2}
            sx={{
              flexGrow: 1,
              px: "4px",
              height: "80vh",
              overflow: "auto",
              marginBottom: "20px",
              // Ẩn scrollbar nhưng vẫn scroll được
              scrollbarWidth: "none", // Firefox
              "&::-webkit-scrollbar": {
                display: "none", // Chrome, Safari, Edge
              },
              "-ms-overflow-style": "none", // IE
            }}
          >
            {sections.map((section, index) => {
              return (
                <SideNavSection
                  items={section.items}
                  key={index}
                  pathname={pathname}
                  subheader={section.subheader}
                  isCollapsed={isCollapsed}
                />
              );
            })}
          </Stack>
          {!isCollapsed && (
            <Box
              sx={{
                background: "#0045FF",
                borderRadius: "10px 10px 0 0",
                padding: "25px 10px 15px 10px",
                width: "90%",
                margin: "0 auto",
              }}
            >
              <Stack
                sx={{ display: "flex", flexDirection: "column", gap: 2, position: "relative" }}
              >
                <Stack
                  flexDirection={"row"}
                  alignItems={"center"}
                  justifyContent={"space-between"}
                  gap={2}
                >
                  <Stack flexDirection={"row"} gap={"5px"} alignItems={"center"}>
                    <Box>
                      <img src="/logo/logo-evotech.png" />
                    </Box>
                    <Box>
                      <Stack>
                        <Typography sx={{ fontSize: "14px", fontWeight: 400, color: "#fff" }}>
                          EvotechCDP
                        </Typography>
                        <Typography sx={{ fontSize: "10px", fontWeight: 400, color: "#fff" }}>
                          Phiên bản: v1
                        </Typography>
                      </Stack>
                    </Box>
                  </Stack>
                  {activePackage && activePackage.packageName ? (
                    <TruncatedText
                      typographyProps={{
                        fontSize: "10px",
                        color: "#fff",
                      }}
                      text={activePackage.packageName}
                    >
                      {/* Temporarily hidden package upgrade link */}
                      {/* {isAgency && (
                        <Typography
                          sx={{
                            color: "#FFFF00",
                            fontStyle: "italic",
                            fontSize: "10px",
                            fontWeight: "400",
                            position: "absolute",
                            top: -18,
                            right: 0,
                          }}
                        >
                          Nâng cấp
                        </Typography>
                      )} */}
                    </TruncatedText>
                  ) : (
                    <Typography
                      style={{
                        fontSize: "10px",
                        fontWeight: 400,
                        color: "#fff",
                        position: "relative",
                      }}
                    >
                      Chưa có gói
                      {/* Temporarily hidden package purchase link */}
                      {/* {isAgency && (
                        <Typography
                          sx={{
                            color: "#FFFF00",
                            fontStyle: "italic",
                            fontSize: "12px",
                            fontWeight: "400",
                            position: "absolute",
                            top: -18,
                            right: 0,
                          }}
                        >
                          Mua gói
                        </Typography>
                      )} */}
                    </Typography>
                  )}
                </Stack>
                <Collapse in={isDetailsVisible}>
                  <Box sx={{ display: "flex", flexDirection: "column", gap: "12px" }}>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <Typography sx={{ color: "#FFFFFF", fontSize: "12px", fontWeight: "400" }}>
                        Tài khoản
                      </Typography>
                      <Typography sx={{ color: "#FFFFFF", fontSize: "12px", fontWeight: "700" }}>
                        {formatDisplayPhoneNumber(profile.phoneNumber)}
                      </Typography>
                    </Box>
                    {activePackage && activePackage.startDate && (
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <Typography sx={{ color: "#FFFFFF", fontSize: "12px", fontWeight: "400" }}>
                          Ngày kích hoạt
                        </Typography>
                        <Typography sx={{ color: "#FFFFFF", fontSize: "12px", fontWeight: "700" }}>
                          {formatDateDisplay(activePackage.startDate)}
                        </Typography>
                      </Box>
                    )}
                    {activePackage && activePackage.endDate && (
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <Typography sx={{ color: "#FFFFFF", fontSize: "12px", fontWeight: "400" }}>
                          Ngày hết hạn
                        </Typography>
                        <Typography sx={{ color: "#FFFFFF", fontSize: "12px", fontWeight: "700" }}>
                          {formatDateDisplay(activePackage.endDate)}
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </Collapse>
                <img
                  width={"10px"}
                  height={"10px"}
                  style={{
                    position: "absolute",
                    left: "46%",
                    top: "-18px",
                    cursor: "pointer",
                    transform: isDetailsVisible ? "rotate(180deg)" : "rotate(0deg)",
                    transition: "transform 0.3s ease",
                  }}
                  src="/logo/icon-double-up.png"
                  onClick={handleToggleDetails}
                />
              </Stack>
            </Box>
          )}
          <Box sx={{ p: 0 }}></Box>
        </Stack>
      </Scrollbar>
    </Drawer>
  );
};

SideNav.propTypes = {
  color: PropTypes.oneOf(["blend-in", "discrete", "evident"]),
  sections: PropTypes.array,
};
