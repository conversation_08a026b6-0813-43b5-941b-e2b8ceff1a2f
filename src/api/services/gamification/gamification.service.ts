import { apiClient } from "../../config/api-client";
import { API_PATHS } from "../../constants/api-paths";
import type { ApiError, ErrorConfig } from "../../types/error.types";
import type { ExtendedRequestConfig } from "../../types/api.types";

function handleApiError(error: any): never {
  const status = error.response?.status || 500;
  const errorData = error.response?.data as ApiError;
  const errorMessage = errorData?.detail || errorData?.title || error.message;

  if (error.response) {
    throw {
      ...error,
      message: errorMessage,
    };
  }

  throw error;
}

export const gamificationService = {
  createGameBrand: async (shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.post(`${API_PATHS.GAMIFICATION.GAME_BRAND}`, shopId, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  listCampaign: async (shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get(
        `${API_PATHS.GAMIFICATION.CAMPAIGN}?shopId=${shopId}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getCampaign: async (campaignId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get(
        `${API_PATHS.GAMIFICATION.CAMPAIGN}/${campaignId}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },

  createCampaign: async (data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };

    try {
      const response = await apiClient.post(`${API_PATHS.GAMIFICATION.CAMPAIGN}`, data, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  activeCampaign: async (shopId: string, campaignId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put(
        `${API_PATHS.GAMIFICATION.CAMPAIGN}/${campaignId}/activate?shopId=${shopId}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getInfo: async (shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get(
        `${API_PATHS.GAMIFICATION.SHOP}?shopId=${shopId}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  activate: async (shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put(
        `${API_PATHS.GAMIFICATION.SHOP}/activate?shopId=${shopId}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  deactivate: async (shopId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put(
        `${API_PATHS.GAMIFICATION.SHOP}/deactivate?shopId=${shopId}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getPrizes: async (campaignId: string, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get(
        `${API_PATHS.GAMIFICATION.PRIZE}?campaignId=${campaignId}`,
        config
      );

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  updatePrizes: async (data: any, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.put(`${API_PATHS.GAMIFICATION.PRIZE}`, data, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  createPrize: async (data: FormData, errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    };
    try {
      const response = await apiClient.post(`${API_PATHS.GAMIFICATION.PRIZE}`, data, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
  getGames: async (errorConfig?: ErrorConfig) => {
    const config: ExtendedRequestConfig = {
      errorHandling: errorConfig,
    };

    try {
      const response = await apiClient.get(`${API_PATHS.GAMIFICATION.GAME}`, config);

      return response;
    } catch (error: any) {
      handleApiError(error);
    }
  },
};
