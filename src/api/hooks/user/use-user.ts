import { useState } from "react";
import { ErrorHandlerService } from "../../services/error-handler.service";
import { userService } from "../../services/user/user.service";

export const useUser = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createUser = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.createUser(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const createUserAddress = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.createUserAddress(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const detailUser = async (shopId: string, data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.detailUser(shopId, data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateUser = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.updateUser(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listTag = async (queryData: any, bodyData: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.listTag(queryData, bodyData);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listAddress = async (queryData: any, bodyData: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.listAddress(queryData, bodyData);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateUserAddress = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.updateUserAddress(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listUser = async (queryData: any = "?skip=0&limit=99", bodyData: any = { search: "" }) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.listUser(queryData, bodyData);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const deleteUserAddress = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.deleteUserAddress(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const deleteUsers = async (shopId: string, userIds: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.deleteUsers(shopId, userIds);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const listUserByUserIds = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.listUserByUserIds(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const updateUserInfo = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.updateUserInfo(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  const exportUserTemplate = async (shopId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.exportUserTemplate(shopId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const importListUser = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.importListUser(data.file, data.shopId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: false,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };
  const exportListUser = async (data: {
    Search?: string;
    TagName?: string;
    ShopId?: string;
    AffiliationStatus?: string;
  }) => {
    try {
      setLoading(true);
      setError(null);
      const response = await userService.exportListUser(data);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: false,
        logError: true,
      });
      setError(errorResponse.detail);
    } finally {
      setLoading(false);
    }
  };

  return {
    createUser,
    createUserAddress,
    detailUser,
    loading,
    error,
    listTag,
    listAddress,
    updateUserAddress,
    updateUser,
    listUser,
    deleteUserAddress,
    deleteUsers,
    listUserByUserIds,
    updateUserInfo,
    exportUserTemplate,
    importListUser,
    exportListUser,
  };
};
