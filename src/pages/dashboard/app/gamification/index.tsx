import React, { useEffect, useState, useCallback } from "react";
import DashboardLayout from "../../../../layouts/dashboard";
import { useGamification } from "@/src/api/hooks/gamification/use-gamification";
import { useStoreId } from "@/src/hooks/use-store-id";
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CircularProgress,
  IconButton,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Checkbox,
  FormControlLabel,
  Switch,
  Chip,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import { VOUCHER_TYPE, CODE_TYPE } from "@/src/api/types/voucher.type";
import { Settings, PowerOff } from "@mui/icons-material";
import { useRouter } from "next/router";
import { PrizeType } from "@/src/constants/constant";
import { Prize } from "@/src/api/types/prize.type";
import { useAppSelector } from "@/src/redux/hooks";
import { paths } from "@/src/paths";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

interface NewCampaignData {
  name: string;
  game: string;
  startTime: string;
  endTime: string;
}

const PRIZE_TYPES = [
  { value: PrizeType.Digital, label: "Kỹ thuật số" },
  { value: PrizeType.Ticket, label: "Lượt chơi" },
  { value: PrizeType.Physical, label: "Voucher" },
];

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
      style={{ height: "100%", width: "100%" }}
    >
      {value === index && <Box sx={{ p: 3, height: "100%", width: "100%" }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

const Gamification: React.FC = () => {
  const snackbar = useSnackbar();
  const {
    createGameBrand,
    listCampaign,
    createCampaign,
    activeCampaign,
    deactivate,
    activate,
    getGames,
    loading,
  } = useGamification();
  const storeId = useStoreId();
  const [tabValue, setTabValue] = useState(0);

  const initialNewPrizeFormData: Partial<Omit<Prize, "id">> = {
    name: "",
    type: PRIZE_TYPES[0].value,
    avatarLink: null,
    quantity: 1,
    stock: null,
    externalPrizeId: null,
  };
  const [campaigns, setCampaigns] = useState<any[]>([]);
  const router = useRouter();
  const [isAddCampaignDialogOpen, setIsAddCampaignDialogOpen] = useState(false);
  const [games, setGames] = useState<any[]>([]);
  const [newCampaignFormData, setNewCampaignFormData] = useState<Partial<NewCampaignData>>({
    name: "",
    game: "",
    startTime: "",
    endTime: "",
  });
  const [campaignThumbnailFile, setCampaignThumbnailFile] = useState<File | null>(null);
  const [campaignThumbnailPreview, setCampaignThumbnailPreview] = useState<string | null>(null);
  const [campaignThumbnailError, setCampaignThumbnailError] = useState<string>("");
  const currentShop = useAppSelector((state) => state.shop.currentShop);
  const [showOaIdDialog, setShowOaIdDialog] = useState(false);
  const [showToggleDialog, setShowToggleDialog] = useState(false);

  // Kiểm tra xem có chiến dịch nào đang hoạt động không
  const hasActiveCampaign = campaigns.some((campaign) => campaign.active);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleGoToCampaignDetail = (campaignId: string) => {
    router.push(`/dashboard/app/gamification/campaign/${campaignId}`);
  };

  const handleToggleAllCampaigns = async () => {
    try {
      if (hasActiveCampaign) {
        // Tắt toàn bộ chiến dịch
        const response = await deactivate(storeId);
        if (response && response.data.result) {
          snackbar.success("Đã tắt toàn bộ chiến dịch game thành công!");
          setShowToggleDialog(false);
          fetchCampaign();
        } else {
          snackbar.error(response?.data?.message || "Có lỗi xảy ra khi tắt chiến dịch.");
        }
      } else {
        // Bật toàn bộ chiến dịch
        const response = await activate(storeId);
        if (response && response.data.result) {
          snackbar.success("Đã bật toàn bộ chiến dịch game thành công!");
          setShowToggleDialog(false);
          fetchCampaign();
        } else {
          snackbar.error(response?.data?.message || "Có lỗi xảy ra khi bật chiến dịch.");
        }
      }
    } catch (error: any) {
      snackbar.error(error?.message || "Có lỗi xảy ra khi thực hiện thao tác.");
    }
  };

  const handleOpenAddCampaignDialog = () => {
    if (!currentShop?.oaId || currentShop?.oaId === "") {
      setShowOaIdDialog(true);
      return;
    }
    setIsAddCampaignDialogOpen(true);
    setNewCampaignFormData((prev) => ({
      ...prev,
      game: games[0]?.id || "",
    }));
  };

  const handleCloseAddCampaignDialog = () => {
    setIsAddCampaignDialogOpen(false);
    setNewCampaignFormData({
      name: "",
      game: games[0]?.id || "",
      startTime: "",
      endTime: "",
    });
    setCampaignThumbnailFile(null);
    setCampaignThumbnailPreview(null);
  };

  const handleAddCampaignFormChange = (
    event: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | { name?: string; value: unknown }
    >
  ) => {
    const { name, value } = event.target;
    setNewCampaignFormData((prev) => ({ ...prev, [name!]: value }));
  };

  const handleCampaignThumbnailChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setCampaignThumbnailFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setCampaignThumbnailPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
      setCampaignThumbnailError("");
    } else {
      setCampaignThumbnailFile(null);
      setCampaignThumbnailPreview(null);
    }
  };

  const handleSaveNewCampaign = async () => {
    if (!newCampaignFormData.name) {
      snackbar.error("Tên chiến dịch không được để trống.");
      return;
    }
    if (!campaignThumbnailFile) {
      setCampaignThumbnailError("Vui lòng chọn ảnh đại diện chiến dịch.");
      return;
    }
    if (!newCampaignFormData.startTime || !newCampaignFormData.endTime) {
      snackbar.error("Thời gian bắt đầu và kết thúc không được để trống.");
      return;
    }
    if (new Date(newCampaignFormData.startTime) >= new Date(newCampaignFormData.endTime)) {
      snackbar.error("Thời gian kết thúc phải sau thời gian bắt đầu.");
      return;
    }
    const formData = new FormData();
    formData.append("shopId", storeId);
    formData.append("name", newCampaignFormData.name || "");
    formData.append("startTime", newCampaignFormData.startTime || "");
    formData.append("endTime", newCampaignFormData.endTime || "");
    formData.append("gameId", newCampaignFormData.game || "");
    formData.append("thumbnail", campaignThumbnailFile);
    try {
      const response = await createCampaign(formData);
      if (response && response.data.result) {
        snackbar.success("Tạo chiến dịch thành công!");
        handleCloseAddCampaignDialog();
        fetchCampaign();
      } else {
        snackbar.error(response?.data?.message || "Có lỗi xảy ra khi tạo chiến dịch.");
      }
    } catch (error: any) {
      snackbar.error(error?.message || "Có lỗi xảy ra khi tạo chiến dịch.");
    }
  };
  const fetchCampaign = async () => {
    const response = await listCampaign(storeId);
    if (response && response.data.result) {
      setCampaigns(response.data.result);
    } else {
      setCampaigns([]);
    }
  };
  useEffect(() => {
    if (!storeId) return;
    const fetchGamificationData = async () => {
      await createGameBrand(storeId);
      fetchCampaign();
      const gamesResponse = await getGames();
      if (gamesResponse && gamesResponse.data && Array.isArray(gamesResponse.data.result)) {
        setGames(gamesResponse.data.result);
        setNewCampaignFormData((prev) => ({
          ...prev,
          game: gamesResponse.data.result[0]?.id || "",
        }));
      }
    };

    fetchGamificationData();
  }, [storeId]);

  return (
    <DashboardLayout>
      <Box
        sx={{
          width: "100%",
          height: "100%",
          display: "flex",
          flexDirection: "column",
          background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
          minHeight: "100vh",
        }}
      >
        {/* Header Section */}
        <Box
          sx={{
            background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
            borderBottom: "1px solid #e0e0e0",
            px: 4,
            py: 3,
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.05)",
            position: "relative",
          }}
        >
          <Box sx={{ mb: 2 }}>
            <Box
              sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 1 }}
            >
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 700,
                  color: "#1a1a1a",
                }}
              >
                Quản lý Game
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography
                  variant="body2"
                  sx={{
                    color: hasActiveCampaign ? "#4caf50" : "#666",
                    fontWeight: 600,
                    fontSize: "0.875rem",
                  }}
                >
                  {hasActiveCampaign ? "Đang hoạt động" : "Tắt"}
                </Typography>
                <Switch
                  checked={hasActiveCampaign}
                  onChange={() => setShowToggleDialog(true)}
                  sx={{
                    "& .MuiSwitch-switchBase.Mui-checked": {
                      color: "#4caf50",
                      "&:hover": {
                        backgroundColor: "rgba(76, 175, 80, 0.08)",
                      },
                    },
                    "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
                      backgroundColor: "#4caf50",
                    },
                    "& .MuiSwitch-track": {
                      backgroundColor: "#e0e0e0",
                    },
                    "& .MuiSwitch-thumb": {
                      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.2)",
                    },
                  }}
                />
              </Box>
            </Box>
            <Typography
              variant="body1"
              sx={{
                color: "#666",
                fontSize: "1rem",
              }}
            >
              Tạo và quản lý các chiến dịch game tương tác với khách hàng
            </Typography>
          </Box>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="gamification tabs"
            sx={{
              "& .MuiTabs-indicator": {
                backgroundColor: "#1976d2",
                height: 3,
                borderRadius: "3px 3px 0 0",
              },
            }}
          >
            <Tab
              label="Chiến dịch Game"
              {...a11yProps(0)}
              sx={{
                color: "#666",
                fontSize: "16px",
                fontWeight: 600,
                textTransform: "none",
                px: 0,
                mr: 4,
                "&.Mui-selected": {
                  color: "#1976d2",
                  fontWeight: 700,
                },
              }}
            />
          </Tabs>
        </Box>
        <Box sx={{ flex: 1, p: 4 }}>
          <TabPanel value={tabValue} index={0}>
            {loading && (
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "400px",
                  background: "white",
                  borderRadius: 3,
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                }}
              >
                <CircularProgress
                  size={48}
                  sx={{
                    color: "#1976d2",
                    mb: 2,
                  }}
                />
                <Typography
                  variant="h6"
                  sx={{
                    color: "#666",
                    fontWeight: 500,
                  }}
                >
                  Đang tải chiến dịch...
                </Typography>
              </Box>
            )}
            {!loading && (
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={4} lg={3}>
                  <Card
                    sx={{
                      height: 400, // Same height as campaign cards
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      justifyContent: "center",
                      cursor: "pointer",
                      border: "2px dashed #e0e0e0",
                      borderRadius: 3,
                      background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
                      transition: "all 0.3s ease",
                      "&:hover": {
                        border: "2px dashed #1976d2",
                        background: "linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)",
                        transform: "translateY(-4px)",
                        boxShadow: "0 8px 25px rgba(25, 118, 210, 0.15)",
                      },
                    }}
                    onClick={handleOpenAddCampaignDialog}
                  >
                    <Box
                      sx={{
                        width: 80,
                        height: 80,
                        borderRadius: "50%",
                        background: "linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        mb: 2,
                        boxShadow: "0 4px 15px rgba(25, 118, 210, 0.3)",
                      }}
                    >
                      <AddIcon sx={{ fontSize: 40, color: "white" }} />
                    </Box>
                    <Typography
                      variant="h6"
                      sx={{
                        color: "#1976d2",
                        fontWeight: 600,
                        textAlign: "center",
                        px: 2,
                      }}
                    >
                      Tạo chiến dịch mới
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#666",
                        textAlign: "center",
                        mt: 1,
                        px: 2,
                      }}
                    >
                      Bắt đầu tạo game mới cho khách hàng
                    </Typography>
                  </Card>
                </Grid>
                {campaigns.map((campaign: any) => (
                  <Grid item xs={12} sm={6} md={4} lg={3} key={campaign.id}>
                    <Card
                      sx={{
                        position: "relative",
                        display: "flex",
                        flexDirection: "column",
                        height: 400, // Fixed height for all cards
                        borderRadius: 3,
                        border: campaign.active ? "2px solid #1976d2" : "1px solid #e0e0e0",
                        boxShadow: campaign.active
                          ? "0 8px 25px rgba(25, 118, 210, 0.2)"
                          : "0 2px 8px rgba(0, 0, 0, 0.1)",
                        background: campaign.active
                          ? "linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%)"
                          : "#fff",
                        transition: "all 0.3s ease",
                        overflow: "hidden",
                        "&:hover": {
                          transform: "translateY(-6px)",
                          boxShadow: campaign.active
                            ? "0 12px 35px rgba(25, 118, 210, 0.3)"
                            : "0 8px 25px rgba(0, 0, 0, 0.15)",
                        },
                      }}
                    >
                      {campaign.active && (
                        <Box sx={{ position: "absolute", top: 16, right: 16, zIndex: 2 }}>
                          <Chip
                            label="Đang hoạt động"
                            sx={{
                              background: "linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)",
                              color: "white",
                              fontWeight: 600,
                              fontSize: "0.75rem",
                              boxShadow: "0 2px 8px rgba(76, 175, 80, 0.4)",
                              border: "2px solid rgba(255, 255, 255, 0.3)",
                            }}
                            size="small"
                          />
                        </Box>
                      )}
                      <Box sx={{ position: "relative", overflow: "hidden" }}>
                        <CardMedia
                          component="img"
                          image={campaign.thumbnailLink}
                          alt={campaign.name}
                          sx={{
                            width: "100%",
                            height: "180px",
                            objectFit: "cover",
                            transition: "transform 0.3s ease",
                            "&:hover": {
                              transform: "scale(1.05)",
                            },
                          }}
                        />
                        <Box
                          sx={{
                            position: "absolute",
                            bottom: 0,
                            left: 0,
                            right: 0,
                            height: "50%",
                            background: "linear-gradient(transparent, rgba(0, 0, 0, 0.1))",
                            pointerEvents: "none",
                          }}
                        />
                      </Box>
                      <CardContent
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "flex-start",
                          p: 3,
                          flexGrow: 1,
                        }}
                      >
                        <Box sx={{ flex: 1 }}>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 700,
                              color: "#1a1a1a",
                              mb: 1,
                              lineHeight: 1.3,
                            }}
                          >
                            {campaign.name}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{
                              color: "#666",
                              fontSize: "0.875rem",
                            }}
                          >
                            Game tương tác khách hàng
                          </Typography>
                        </Box>
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={<Settings />}
                          onClick={() => handleGoToCampaignDetail(campaign.id)}
                          sx={{
                            borderRadius: 2,
                            textTransform: "none",
                            fontWeight: 600,
                            px: 2,
                            py: 1,
                            border: "1px solid #e0e0e0",
                            color: "#666",
                            "&:hover": {
                              border: "1px solid #1976d2",
                              color: "#1976d2",
                              background: "rgba(25, 118, 210, 0.04)",
                            },
                          }}
                        >
                          Cài đặt
                        </Button>
                      </CardContent>
                      <CardActions sx={{ p: 3, pt: 0, mt: "auto" }}>
                        <Button
                          variant={campaign.active ? "contained" : "contained"}
                          disabled={campaign.active}
                          onClick={async () => {
                            if (!campaign.active && storeId) {
                              await activeCampaign(storeId, campaign.id);
                              fetchCampaign();
                            }
                          }}
                          sx={{
                            width: "100%",
                            py: 1.5,
                            borderRadius: 2,
                            fontWeight: 600,
                            textTransform: "none",
                            fontSize: "0.95rem",
                            ...(campaign.active
                              ? {
                                  background: "linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)",
                                  color: "white",
                                  boxShadow: "0 4px 15px rgba(76, 175, 80, 0.3)",
                                  "&:disabled": {
                                    background: "linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)",
                                    color: "white",
                                    opacity: 0.8,
                                  },
                                }
                              : {
                                  background: "linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)",
                                  color: "white",
                                  boxShadow: "0 4px 15px rgba(25, 118, 210, 0.3)",
                                  "&:hover": {
                                    background: "linear-gradient(135deg, #1565c0 0%, #1976d2 100%)",
                                    boxShadow: "0 6px 20px rgba(25, 118, 210, 0.4)",
                                    transform: "translateY(-1px)",
                                  },
                                }),
                          }}
                        >
                          {campaign.active ? "✓ Đang hoạt động" : "Kích hoạt chiến dịch"}
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </TabPanel>
        </Box>
      </Box>
      {/* Add Campaign Dialog */}
      <Dialog
        open={isAddCampaignDialogOpen}
        onClose={handleCloseAddCampaignDialog}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>Tạo chiến dịch mới</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <Typography variant="subtitle2" mb={1}>
              Tên chiến dịch <span style={{ color: "red" }}>*</span>
            </Typography>
            <TextField
              name="name"
              value={newCampaignFormData.name || ""}
              onChange={handleAddCampaignFormChange}
              fullWidth
              required
            />
            {/* Thumbnail upload */}
            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Ảnh đại diện chiến dịch <span style={{ color: "red" }}>*</span>
              </Typography>
              {campaignThumbnailPreview && (
                <Box
                  sx={{
                    mt: 1,
                    mb: 1,
                    border: "1px dashed #ccc",
                    borderRadius: "4px",
                    width: 150,
                    height: 150,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    overflow: "hidden",
                  }}
                >
                  <img
                    src={campaignThumbnailPreview}
                    alt="Xem trước"
                    style={{ maxWidth: "100%", maxHeight: "100%", objectFit: "contain" }}
                  />
                </Box>
              )}
              <Button variant="outlined" component="label" size="small" sx={{ mr: 1 }}>
                {campaignThumbnailFile ? "Thay đổi ảnh" : "Tải ảnh lên"}
                <input
                  type="file"
                  hidden
                  accept="image/*"
                  onChange={handleCampaignThumbnailChange}
                />
              </Button>
              {campaignThumbnailError && (
                <Typography variant="caption" color="error" sx={{ mt: 1, display: "block" }}>
                  {campaignThumbnailError}
                </Typography>
              )}
            </Box>
            <FormControl fullWidth margin="normal" required>
              <Typography variant="subtitle2" gutterBottom>
                Game <span style={{ color: "red" }}>*</span>
              </Typography>
              <Select
                name="game"
                value={newCampaignFormData.game || ""}
                onChange={(e) => handleAddCampaignFormChange(e as any)}
              >
                {games.map((game) => (
                  <MenuItem key={game.id} value={game.id}>
                    {game.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Thời gian bắt đầu <span style={{ color: "red" }}>*</span>
              </Typography>
              <TextField
                name="startTime"
                type="datetime-local"
                value={newCampaignFormData.startTime || ""}
                onChange={handleAddCampaignFormChange}
                fullWidth
                required
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Box>

            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Thời gian kết thúc <span style={{ color: "red" }}>*</span>
              </Typography>
              <TextField
                name="endTime"
                type="datetime-local"
                value={newCampaignFormData.endTime || ""}
                onChange={handleAddCampaignFormChange}
                fullWidth
                required
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAddCampaignDialog}>Hủy</Button>
          <Button onClick={handleSaveNewCampaign} color="primary" variant="contained">
            Lưu
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog open={showOaIdDialog} onClose={() => setShowOaIdDialog(false)}>
        <DialogTitle>Thiếu thông tin Zalo OAID</DialogTitle>
        <DialogContent>
          <Typography>
            Vui lòng cập nhật thông tin Zalo OAID cho cửa hàng trước khi tạo chiến dịch mới.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowOaIdDialog(false)}>Đóng</Button>
          <Button
            onClick={() => {
              setShowOaIdDialog(false);
              router.push(paths.dashboard.store.generalSettings);
            }}
            color="primary"
            variant="contained"
          >
            Cập nhật ngay
          </Button>
        </DialogActions>
      </Dialog>

      {/* Toggle All Campaigns Dialog */}
      <Dialog
        open={showToggleDialog}
        onClose={() => setShowToggleDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 2,
            color: hasActiveCampaign ? "#f44336" : "#666",
            fontWeight: 600,
          }}
        >
          {hasActiveCampaign ? (
            <PowerOff sx={{ color: "#f44336" }} />
          ) : (
            <Settings sx={{ color: "#666" }} />
          )}
          {hasActiveCampaign ? "Tắt toàn bộ chiến dịch game" : "Bật toàn bộ chiến dịch game"}
        </DialogTitle>
        <DialogContent>
          {hasActiveCampaign ? (
            <>
              <Typography sx={{ mb: 2 }}>
                Bạn có chắc chắn muốn tắt toàn bộ chiến dịch game của shop không?
              </Typography>
              <Typography variant="body2" sx={{ color: "#666" }}>
                Hành động này sẽ ngừng hoạt động tất cả các chiến dịch game đang chạy. Bạn có thể
                kích hoạt lại sau này.
              </Typography>
            </>
          ) : (
            <>
              <Typography sx={{ mb: 2 }}>
                Bạn có chắc chắn muốn bật toàn bộ chiến dịch game của shop không?
              </Typography>
              <Typography variant="body2" sx={{ color: "#666" }}>
                Hành động này sẽ kích hoạt tất cả các chiến dịch game có sẵn trong shop.
              </Typography>
            </>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3, gap: 1 }}>
          <Button
            onClick={() => setShowToggleDialog(false)}
            variant="outlined"
            sx={{
              borderColor: "#e0e0e0",
              color: "#666",
              "&:hover": {
                borderColor: "#1976d2",
                color: "#1976d2",
              },
            }}
          >
            Hủy
          </Button>
          <Button
            onClick={handleToggleAllCampaigns}
            variant="contained"
            sx={{
              background: hasActiveCampaign
                ? "linear-gradient(135deg, #f44336 0%, #e57373 100%)"
                : "linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)",
              color: "white",
              fontWeight: 600,
              "&:hover": {
                background: hasActiveCampaign
                  ? "linear-gradient(135deg, #d32f2f 0%, #f44336 100%)"
                  : "linear-gradient(135deg, #388e3c 0%, #4caf50 100%)",
              },
            }}
          >
            {hasActiveCampaign ? "Tắt toàn bộ" : "Bật toàn bộ"}
          </Button>
        </DialogActions>
      </Dialog>
    </DashboardLayout>
  );
};

export default Gamification;
