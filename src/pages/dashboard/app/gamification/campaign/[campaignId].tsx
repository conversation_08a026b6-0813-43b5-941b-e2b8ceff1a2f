import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import DashboardLayout from "src/layouts/dashboard";
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Grid,
  Card,
  CardMedia,
  CardContent,
  IconButton,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  Chip,
  SelectChangeEvent,
  CircularProgress,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useGamification } from "@/src/api/hooks/gamification/use-gamification";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import {
  VOUCHER_TYPE,
  CODE_TYPE,
  DISCOUNT_TYPE,
  TYPE_DISTRIBUTION,
} from "@/src/api/types/voucher.type";
import { useStoreId } from "@/src/hooks/use-store-id";
import { PRIZE_CATEGORY, PRIZE_TYPES, PrizeType } from "@/src/constants/constant";
import { Prize } from "@/src/api/types/prize.type";
import { useProduct } from "@/src/api/hooks/dashboard/product/use-product";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";

const CampaignDetailPage: React.FC = () => {
  const router = useRouter();
  const { campaignId: campaignIdFromQuery } = router.query;
  const campaignId = Array.isArray(campaignIdFromQuery)
    ? campaignIdFromQuery[0]
    : campaignIdFromQuery;

  const snackbar = useSnackbar();
  const { getCampaign, getPrizes, createPrize, updatePrizes, loading } = useGamification();
  const { listVoucher } = useVoucher();
  const storeId = useStoreId();
  const { getProduct } = useProduct();

  const [tabValue, setTabValue] = useState(0);
  const [iframeSrc, setIframeSrc] = useState<string | null>(null);
  const [prizes, setPrizes] = useState<Prize[]>([]);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedPrize, setSelectedPrize] = useState<Prize | null>(null);
  const [editFormData, setEditFormData] = useState<Partial<Prize>>({});
  const [newAvatarFile, setNewAvatarFile] = useState<File | null>(null);
  const [avatarPreviewUrl, setAvatarPreviewUrl] = useState<string | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const initialNewPrizeFormData: Partial<Omit<Prize, "id">> = {
    name: "",
    type: PRIZE_TYPES[0].value,
    avatarLink: null,
    quantity: 1,
    stock: null,
    externalPrizeId: null,
    category: PRIZE_CATEGORY.Other,
  };
  const [addFormData, setAddFormData] =
    useState<Partial<Omit<Prize, "id">>>(initialNewPrizeFormData);
  const [addNewAvatarFile, setAddNewAvatarFile] = useState<File | null>(null);
  const [addAvatarPreviewUrl, setAddAvatarPreviewUrl] = useState<string | null>(null);
  const [vouchers, setVouchers] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);

  // Track which tabs have been loaded
  const [loadedTabs, setLoadedTabs] = useState<Set<number>>(new Set([0])); // Load tab 0 by default

  const [isSavingAdd, setIsSavingAdd] = useState(false);
  const [isSavingEdit, setIsSavingEdit] = useState(false);

  const fetchCampaignData = async () => {
    if (campaignId) {
      try {
        const response = await getCampaign(campaignId as string);
        if (response && response.data.result) {
          const { dashboardUrl, accessToken, refreshToken } = response.data.result;
          const src = `${dashboardUrl}/campaigns/${campaignId}?embedded=true&access_token=${accessToken}&refresh_token=${refreshToken}`;
          setIframeSrc(src);
        }
      } catch (error) {
        console.error("Failed to fetch campaign data", error);
      }
    }
  };

  // Initial load for tab 0 (default tab)
  useEffect(() => {
    if (router.isReady && campaignId) {
      fetchCampaignData();
    }
  }, [campaignId, router.isReady]);

  // Prize logic
  const fetchPrizesData = async () => {
    if (!campaignId) return;
    const response = await getPrizes(campaignId as string);
    if (response && response.status === 200 && response.data) {
      setPrizes(response.data);
    } else {
      setPrizes([]);
    }
  };

  const fetchVouchers = async () => {
    try {
      const response = await listVoucher({
        skip: 0,
        limit: 100,
        shopId: storeId,
        distributionType: [TYPE_DISTRIBUTION.ON_RECEIVE],
        codeType: CODE_TYPE.UNIQUE,
      });
      if (response && response.status === 200 && response.data) {
        setVouchers(response.data.result.result);
      } else {
        setVouchers([]);
      }
    } catch (error) {
      setVouchers([]);
      snackbar.error("Không thể tải danh sách voucher.");
    }
  };

  // Load data when tab becomes active and is marked as loaded
  useEffect(() => {
    if (!campaignId || !storeId) return;
    if (tabValue === 1 && loadedTabs.has(1)) {
      fetchPrizesData();
      fetchVouchers();
    }
  }, [campaignId, storeId, tabValue, loadedTabs]);

  useEffect(() => {
    if (
      (isAddDialogOpen || isEditDialogOpen) &&
      (addFormData.category === PRIZE_CATEGORY.Product ||
        editFormData.category === PRIZE_CATEGORY.Product)
    ) {
      (async () => {
        try {
          const res = await getProduct(0, 100, "Product");
          if (res && res.data && Array.isArray(res.data.data)) {
            setProducts(res.data.data);
          } else {
            setProducts([]);
          }
        } catch {
          setProducts([]);
        }
      })();
    }
  }, [isAddDialogOpen, isEditDialogOpen, addFormData.category, editFormData.category]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);

    // Mark this tab as loaded and trigger data loading
    if (!loadedTabs.has(newValue)) {
      setLoadedTabs((prev) => new Set([...prev, newValue]));
    }
  };

  const handleOpenEditDialog = (prize: Prize) => {
    setSelectedPrize(prize);
    setEditFormData(prize);
    setNewAvatarFile(null);
    setAvatarPreviewUrl(prize.avatarLink);
    setIsEditDialogOpen(true);
  };

  const handleCloseEditDialog = () => {
    setSelectedPrize(null);
    setIsEditDialogOpen(false);
    setEditFormData({});
    setNewAvatarFile(null);
    setAvatarPreviewUrl(null);
  };

  const handleEditFormChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent
  ) => {
    const { name, value } = event.target;
    setEditFormData((prev) => ({ ...prev, [name!]: value }));
    if (name === "type") {
      setEditFormData((prev) => ({
        ...prev,
        stock: null,
        // Automatically set category to "Other" when selecting "Lượt chơi" (Ticket)
        category: value === PrizeType.Ticket ? PRIZE_CATEGORY.Other : prev.category,
      }));
    }
    if (name === "category") {
      setEditFormData((prev) => ({ ...prev, stock: null }));
    }
  };

  const handleAvatarFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setNewAvatarFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setNewAvatarFile(null);
      setAvatarPreviewUrl(selectedPrize?.avatarLink || null);
    }
  };

  const handleSavePrize = async () => {
    if (!campaignId || !editFormData.id) {
      return;
    }
    if (!editFormData.name) {
      snackbar.error("Tên phần thưởng không được để trống.");
      return;
    }
    if (editFormData.quantity === undefined || editFormData.quantity <= 0) {
      snackbar.error("Số lượng phát mỗi lần phải lớn hơn 0.");
      return;
    }
    let submissionPayload = { ...editFormData, campaignId, shopId: storeId };
    setIsSavingEdit(true);
    try {
      const response = await updatePrizes(submissionPayload);
      if (response && response.status === 200) {
        handleCloseEditDialog();
        fetchPrizesData();
      }
    } catch (error: any) {}
    setIsSavingEdit(false);
  };

  const handleOpenAddDialog = () => {
    setAddFormData({ ...initialNewPrizeFormData, category: PRIZE_CATEGORY.Voucher });
    setAddNewAvatarFile(null);
    setAddAvatarPreviewUrl(null);
    setIsAddDialogOpen(true);
  };

  const handleCloseAddDialog = () => {
    setIsAddDialogOpen(false);
    setAddFormData(initialNewPrizeFormData);
    setAddNewAvatarFile(null);
    setAddAvatarPreviewUrl(null);
  };

  const handleAddFormChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent
  ) => {
    const { name, value } = event.target;
    setAddFormData((prev) => ({ ...prev, [name!]: value }));
    if (name === "externalPrizeId") {
      const selectedVoucher = vouchers.find((v) => v.voucherId === value);
      if (selectedVoucher) {
        setAddFormData((prev) => ({
          ...prev,
          stock: selectedVoucher.remainingStock,
        }));
      }
    }
    if (name === "type") {
      setAddFormData((prev) => ({
        ...prev,
        stock: null,
        // Automatically set category to "Other" when selecting "Lượt chơi" (Ticket)
        category: value === PrizeType.Ticket ? PRIZE_CATEGORY.Other : prev.category,
      }));
    }
    if (name === "category") {
      setAddFormData((prev) => ({ ...prev, stock: null }));
    }
  };

  const handleAddAvatarFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setAddNewAvatarFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setAddAvatarPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setAddNewAvatarFile(null);
      setAddAvatarPreviewUrl(null);
    }
  };

  const handleSaveNewPrize = async () => {
    if (!campaignId && !storeId) {
      return;
    }
    if (!addFormData.name) {
      snackbar.error("Tên phần thưởng không được để trống.");
      return;
    }
    if (!addNewAvatarFile) {
      snackbar.error("Hình ảnh phần thưởng không được để trống.");
      return;
    }
    if (
      addFormData.type === PrizeType.Physical &&
      addFormData.category === PRIZE_CATEGORY.Voucher &&
      (!addFormData.externalPrizeId || String(addFormData.externalPrizeId).trim() === "")
    ) {
      snackbar.error("Vui lòng chọn voucher cho quà tặng.");
      return;
    }
    if (addFormData.quantity === undefined || addFormData.quantity <= 0) {
      snackbar.error("Số lượng phát mỗi lần phải lớn hơn 0.");
      return;
    }
    if (addFormData.stock === null || addFormData.stock === undefined || addFormData.stock <= 0) {
      snackbar.error("Số lượng tồn kho không được để trống và phải lớn hơn 0.");
      return;
    }
    const prizeDataForCreation = {
      shopId: storeId || "",
      campaignId,
      name: addFormData.name!,
      type: addFormData.type!,
      avatar: addNewAvatarFile,
      quantity: addFormData.quantity!,
      stock: addFormData.stock!,
      externalPrizeId: addFormData.externalPrizeId,
      category: addFormData.category,
    };
    setIsSavingAdd(true);
    try {
      const response = await createPrize(prizeDataForCreation);
      if (response) {
        handleCloseAddDialog();
        fetchPrizesData();
        snackbar.success("Thêm mới quà tặng thành công!");
      } else {
        snackbar.error(response?.data?.message || "Có lỗi xảy ra khi thêm mới quà tặng.");
      }
    } catch (error: any) {
      snackbar.error(error?.message || "Có lỗi xảy ra khi thêm mới quà tặng.");
    }
    setIsSavingAdd(false);
  };

  const handleAddCategoryChange = (event: SelectChangeEvent) => {
    const { name, value } = event.target;
    setAddFormData((prev) => ({ ...prev, [name!]: value, externalPrizeId: null }));
  };

  const handleEditCategoryChange = (event: SelectChangeEvent) => {
    const { name, value } = event.target;
    setEditFormData((prev) => ({ ...prev, [name!]: value, externalPrizeId: null }));
  };

  const handleEditVoucherOrProductChange = (event: SelectChangeEvent) => {
    const { name, value } = event.target;
    setEditFormData((prev) => ({ ...prev, [name!]: value }));

    // Không cập nhật stock trong edit mode vì voucher không thể thay đổi
    // Logic này chỉ dành cho add mode
  };

  return (
    <DashboardLayout>
      <Box
        sx={{
          width: "100%",
          height: "100%",
          display: "flex",
          flexDirection: "column",
          background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
          minHeight: "100vh",
        }}
      >
        {/* Header Section */}
        <Box
          sx={{
            background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
            borderBottom: "1px solid #e0e0e0",
            px: 4,
            py: 3,
            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.05)",
          }}
        >
          <Box sx={{ mb: { xs: 1.5, sm: 2 } }}>
            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
              <IconButton
                onClick={() => router.push("/dashboard/app/gamification")}
                sx={{
                  mr: 1,
                  p: { xs: 0.5, sm: 1 },
                  color: "#666",
                  "&:hover": {
                    color: "#1976d2",
                    backgroundColor: "rgba(25, 118, 210, 0.04)",
                  },
                }}
              >
                <ArrowBackIcon sx={{ fontSize: { xs: "20px", sm: "24px" } }} />
              </IconButton>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 700,
                  color: "#1a1a1a",
                  fontSize: { xs: "1.5rem", sm: "1.75rem", md: "2.125rem" },
                }}
              >
                Chi tiết chiến dịch
              </Typography>
            </Box>
            <Typography
              variant="body1"
              sx={{
                color: "#666",
                fontSize: "1rem",
              }}
            >
              Quản lý cài đặt và thiết lập quà tặng cho chiến dịch game
            </Typography>
          </Box>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="campaign detail tabs"
            sx={{
              "& .MuiTabs-indicator": {
                backgroundColor: "#1976d2",
                height: 3,
                borderRadius: "3px 3px 0 0",
              },
            }}
          >
            <Tab
              label="Cài đặt Game"
              sx={{
                color: "#666",
                fontSize: "16px",
                fontWeight: 600,
                textTransform: "none",
                px: 0,
                mr: 4,
                "&.Mui-selected": {
                  color: "#1976d2",
                  fontWeight: 700,
                },
              }}
            />
            <Tab
              label="Thiết lập quà tặng"
              sx={{
                color: "#666",
                fontSize: "16px",
                fontWeight: 600,
                textTransform: "none",
                px: 0,
                mr: 4,
                "&.Mui-selected": {
                  color: "#1976d2",
                  fontWeight: 700,
                },
              }}
            />
          </Tabs>
        </Box>
        <Box sx={{ flex: 1, p: 4 }}>
          {tabValue === 0 && (
            <Box>
              {loading && (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "400px",
                    background: "white",
                    borderRadius: 3,
                    boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  <CircularProgress
                    size={48}
                    sx={{
                      color: "#1976d2",
                      mb: 2,
                    }}
                  />
                  <Typography
                    variant="h6"
                    sx={{
                      color: "#666",
                      fontWeight: 500,
                    }}
                  >
                    Đang tải cài đặt game...
                  </Typography>
                </Box>
              )}
              {!loading && iframeSrc && (
                <Box
                  sx={{
                    borderRadius: 3,
                    overflow: "hidden",
                    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
                    background: "white",
                  }}
                >
                  <iframe
                    src={iframeSrc}
                    style={{
                      width: "100%",
                      height: "100vh",
                      border: "none",
                    }}
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    referrerPolicy="strict-origin-when-cross-origin"
                    allowFullScreen
                    title="Gamification Dashboard"
                  />
                </Box>
              )}
            </Box>
          )}
          {tabValue === 1 && (
            <Box>
              {prizes.length === 0 && (
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={4} lg={3}>
                    <Card
                      sx={{
                        height: 320,
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                        cursor: "pointer",
                        border: "2px dashed #e0e0e0",
                        borderRadius: 3,
                        background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
                        transition: "all 0.3s ease",
                        "&:hover": {
                          border: "2px dashed #1976d2",
                          background: "linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)",
                          transform: "translateY(-4px)",
                          boxShadow: "0 8px 25px rgba(25, 118, 210, 0.15)",
                        },
                      }}
                      onClick={handleOpenAddDialog}
                    >
                      <Box
                        sx={{
                          width: 80,
                          height: 80,
                          borderRadius: "50%",
                          background: "linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          mb: 2,
                          boxShadow: "0 4px 15px rgba(25, 118, 210, 0.3)",
                        }}
                      >
                        <AddIcon sx={{ fontSize: 40, color: "white" }} />
                      </Box>
                      <Typography
                        variant="h6"
                        sx={{
                          color: "#1976d2",
                          fontWeight: 600,
                          textAlign: "center",
                          px: 2,
                        }}
                      >
                        Thêm quà tặng
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: "#666",
                          textAlign: "center",
                          mt: 1,
                          px: 2,
                        }}
                      >
                        Tạo phần thưởng hấp dẫn cho game
                      </Typography>
                    </Card>
                  </Grid>
                  <Grid item xs={12}>
                    <Box
                      sx={{
                        textAlign: "center",
                        mt: 4,
                        p: 4,
                        background: "white",
                        borderRadius: 3,
                        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                      }}
                    >
                      <Typography
                        variant="h6"
                        sx={{
                          color: "#666",
                          fontWeight: 500,
                          mb: 1,
                        }}
                      >
                        Chưa có quà tặng nào
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: "#999",
                        }}
                      >
                        Hãy thêm quà tặng đầu tiên để thu hút người chơi
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              )}
              {prizes.length > 0 && (
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={4} lg={3}>
                    <Card
                      sx={{
                        height: 320,
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                        cursor: "pointer",
                        border: "2px dashed #e0e0e0",
                        borderRadius: 3,
                        background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
                        transition: "all 0.3s ease",
                        "&:hover": {
                          border: "2px dashed #1976d2",
                          background: "linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)",
                          transform: "translateY(-4px)",
                          boxShadow: "0 8px 25px rgba(25, 118, 210, 0.15)",
                        },
                      }}
                      onClick={handleOpenAddDialog}
                    >
                      <Box
                        sx={{
                          width: 60,
                          height: 60,
                          borderRadius: "50%",
                          background: "linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          mb: 2,
                          boxShadow: "0 4px 15px rgba(25, 118, 210, 0.3)",
                        }}
                      >
                        <AddIcon sx={{ fontSize: 30, color: "white" }} />
                      </Box>
                      <Typography
                        variant="h6"
                        sx={{
                          color: "#1976d2",
                          fontWeight: 600,
                          textAlign: "center",
                          fontSize: "1rem",
                        }}
                      >
                        Thêm mới
                      </Typography>
                    </Card>
                  </Grid>
                  {prizes.map((prize) => (
                    <Grid item xs={12} sm={6} md={4} lg={3} key={prize.id}>
                      <Card
                        sx={{
                          height: 320,
                          display: "flex",
                          flexDirection: "column",
                          borderRadius: 3,
                          boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                          transition: "all 0.3s ease",
                          overflow: "hidden",
                          "&:hover": {
                            transform: "translateY(-4px)",
                            boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)",
                          },
                        }}
                      >
                        <Box sx={{ position: "relative", overflow: "hidden" }}>
                          <CardMedia
                            component="img"
                            image={prize.avatarLink}
                            alt={prize.name}
                            sx={{
                              width: "100%",
                              height: "140px",
                              objectFit: "cover",
                              transition: "transform 0.3s ease",
                              "&:hover": {
                                transform: "scale(1.05)",
                              },
                            }}
                          />
                          <Box
                            sx={{
                              position: "absolute",
                              top: 8,
                              right: 8,
                              background:
                                prize.type === PrizeType.Physical
                                  ? "linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)"
                                  : "linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)",
                              color: "white",
                              px: 1.5,
                              py: 0.5,
                              borderRadius: 2,
                              fontSize: "0.75rem",
                              fontWeight: 600,
                              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.2)",
                            }}
                          >
                            {prize.type === PrizeType.Physical ? "Quà tặng" : "Lượt chơi"}
                          </Box>
                        </Box>
                        <CardContent sx={{ flex: 1, p: 2 }}>
                          <Typography
                            variant="h6"
                            sx={{
                              fontWeight: 700,
                              mb: 1,
                              fontSize: "1rem",
                              color: "#1a1a1a",
                              lineHeight: 1.3,
                            }}
                          >
                            {prize.name}
                          </Typography>
                          {/* Hiển thị thông tin voucher nếu là voucher */}
                          {prize.type === PrizeType.Physical &&
                            prize.category === PRIZE_CATEGORY.Voucher &&
                            prize.externalPrizeId && (
                              <Box sx={{ mb: 1 }}>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: "#1976d2",
                                    fontSize: "0.875rem",
                                    fontWeight: 600,
                                    mb: 0.5,
                                  }}
                                >
                                  Voucher:{" "}
                                  {(() => {
                                    const voucher = vouchers.find(
                                      (v) => v.voucherId === prize.externalPrizeId
                                    );
                                    return voucher ? voucher.voucherName : "Không tìm thấy voucher";
                                  })()}
                                </Typography>
                              </Box>
                            )}
                          <Box sx={{ mb: 1 }}>
                            <Typography
                              variant="body2"
                              sx={{
                                color: "#666",
                                fontSize: "0.875rem",
                                mb: 0.5,
                              }}
                            >
                              Phát mỗi lần: <strong>{prize.quantity}</strong>
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{
                                color: "#666",
                                fontSize: "0.875rem",
                              }}
                            >
                              Tồn kho:{" "}
                              <strong>
                                {prize.stock === null ? "Không giới hạn" : prize.stock}
                              </strong>
                            </Typography>
                          </Box>
                        </CardContent>
                        <CardActions sx={{ p: 2, pt: 0 }}>
                          <Button
                            onClick={() => handleOpenEditDialog(prize)}
                            variant="contained"
                            sx={{
                              width: "100%",
                              py: 1,
                              borderRadius: 2,
                              fontWeight: 600,
                              textTransform: "none",
                              background: "linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)",
                              boxShadow: "0 2px 8px rgba(25, 118, 210, 0.3)",
                              "&:hover": {
                                background: "linear-gradient(135deg, #1565c0 0%, #1976d2 100%)",
                                boxShadow: "0 4px 15px rgba(25, 118, 210, 0.4)",
                                transform: "translateY(-1px)",
                              },
                            }}
                          >
                            Chỉnh sửa
                          </Button>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )}
            </Box>
          )}
        </Box>
      </Box>
      {/* Edit Prize Dialog */}
      <Dialog open={isEditDialogOpen} onClose={handleCloseEditDialog} fullWidth maxWidth="sm">
        <DialogTitle>Chỉnh sửa quà tặng</DialogTitle>
        <DialogContent>
          {selectedPrize && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="subtitle2" mb={1}>
                Tên phần thưởng
              </Typography>
              <TextField
                name="name"
                value={editFormData.name || ""}
                onChange={handleEditFormChange}
                fullWidth
              />
              <Box sx={{ mt: 2, mb: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Hình ảnh phần thưởng
                </Typography>
                {avatarPreviewUrl && (
                  <Box
                    sx={{
                      mt: 1,
                      mb: 1,
                      border: "1px dashed #ccc",
                      borderRadius: "4px",
                      width: 150,
                      height: 150,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      overflow: "hidden",
                    }}
                  >
                    <img
                      src={avatarPreviewUrl}
                      alt="Xem trước"
                      style={{ maxWidth: "100%", maxHeight: "100%", objectFit: "contain" }}
                    />
                  </Box>
                )}
              </Box>
              <Box sx={{ mt: 2, mb: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Số lượng phát mỗi lần
                </Typography>
                <TextField
                  name="quantity"
                  type="number"
                  value={editFormData.quantity === undefined ? "" : editFormData.quantity || 0}
                  onChange={handleEditFormChange}
                  fullWidth
                />
              </Box>
              <Box sx={{ mt: 2, mb: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Số lượng tồn kho{" "}
                  {editFormData.type === PrizeType.Physical ? (
                    "(Tự động theo số lượng voucher còn lại)"
                  ) : (
                    <span style={{ color: "red" }}>*</span>
                  )}
                </Typography>
                <TextField
                  name="stock"
                  type="number"
                  value={
                    editFormData.stock === null || editFormData.stock === undefined
                      ? ""
                      : editFormData.stock
                  }
                  onChange={(e) => {
                    const value = e.target.value;
                    setEditFormData((prev) => ({
                      ...prev,
                      stock: value === "" ? null : Number(value),
                    }));
                  }}
                  fullWidth
                  required={editFormData.type !== PrizeType.Physical}
                  InputProps={{ inputProps: { min: 1 } }}
                  disabled={editFormData.type === PrizeType.Physical}
                />
              </Box>
              {editFormData.type === PrizeType.Physical && (
                <FormControl fullWidth margin="normal" required>
                  <Typography variant="subtitle2" gutterBottom>
                    Loại quà tặng
                  </Typography>
                  <Select
                    labelId="category-edit-select-label"
                    name="category"
                    value={editFormData.category || PRIZE_CATEGORY.Voucher}
                    onChange={handleEditCategoryChange}
                    label="Loại quà tặng"
                    disabled
                  >
                    <MenuItem value={PRIZE_CATEGORY.Voucher}>Voucher</MenuItem>
                  </Select>
                </FormControl>
              )}
              {editFormData.type === PrizeType.Physical &&
                editFormData.category === PRIZE_CATEGORY.Voucher && (
                  <FormControl fullWidth margin="normal">
                    <Typography variant="subtitle2" gutterBottom>
                      Voucher đã chọn
                    </Typography>
                    <Select
                      labelId="voucher-edit-select-label"
                      name="externalPrizeId"
                      value={editFormData.externalPrizeId || ""}
                      onChange={handleEditVoucherOrProductChange}
                      label="Voucher đã chọn"
                      disabled={true}
                    >
                      {vouchers.length === 0 ? (
                        <MenuItem value="" disabled>
                          <em>Không có voucher nào khả dụng</em>
                        </MenuItem>
                      ) : (
                        vouchers.map((voucher) => (
                          <MenuItem key={voucher.voucherId} value={voucher.voucherId}>
                            {voucher.voucherName}
                          </MenuItem>
                        ))
                      )}
                    </Select>
                  </FormControl>
                )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEditDialog}>Hủy</Button>
          <Button
            onClick={handleSavePrize}
            color="primary"
            variant="contained"
            disabled={isSavingEdit}
          >
            {isSavingEdit ? <CircularProgress size={20} sx={{ color: "white", mr: 1 }} /> : null}
            Lưu thay đổi
          </Button>
        </DialogActions>
      </Dialog>
      {/* Add Prize Dialog */}
      <Dialog open={isAddDialogOpen} onClose={handleCloseAddDialog} fullWidth maxWidth="sm">
        <DialogTitle>Thêm mới quà tặng</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <Typography variant="subtitle2" mb={1}>
              Tên phần thưởng
            </Typography>
            <TextField
              name="name"
              value={addFormData.name || ""}
              onChange={handleAddFormChange}
              fullWidth
              required
            />
            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Hình ảnh phần thưởng
              </Typography>
              {addAvatarPreviewUrl && (
                <Box
                  sx={{
                    mt: 1,
                    mb: 1,
                    border: "1px dashed #ccc",
                    borderRadius: "4px",
                    width: 150,
                    height: 150,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    overflow: "hidden",
                  }}
                >
                  <img
                    src={addAvatarPreviewUrl}
                    alt="Xem trước"
                    style={{ maxWidth: "100%", maxHeight: "100%", objectFit: "contain" }}
                  />
                </Box>
              )}
              <Button variant="outlined" component="label" size="small" sx={{ mr: 1 }}>
                {addNewAvatarFile ? "Thay đổi ảnh" : "Tải ảnh lên"}
                <input type="file" hidden accept="image/*" onChange={handleAddAvatarFileChange} />
              </Button>
            </Box>
            <FormControl fullWidth margin="normal" required>
              <Typography variant="subtitle2" gutterBottom>
                Loại phần thưởng
              </Typography>
              <Select
                labelId="prize-type-label"
                name="type"
                value={addFormData.type || ""}
                onChange={handleAddFormChange}
                label="Loại phần thưởng"
              >
                {PRIZE_TYPES.map((type) => (
                  <MenuItem key={type.value} value={type.value}>
                    {type.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            {addFormData.type === PrizeType.Physical && (
              <FormControl fullWidth margin="normal" required>
                <Typography variant="subtitle2" gutterBottom>
                  Loại quà tặng
                </Typography>
                <Select
                  labelId="category-select-label"
                  name="category"
                  value={addFormData.category || PRIZE_CATEGORY.Voucher}
                  onChange={handleAddCategoryChange}
                  label="Loại quà tặng"
                >
                  <MenuItem value={PRIZE_CATEGORY.Voucher}>Voucher</MenuItem>
                </Select>
              </FormControl>
            )}
            {addFormData.type === PrizeType.Physical &&
              addFormData.category === PRIZE_CATEGORY.Voucher && (
                <FormControl fullWidth margin="normal">
                  <Typography variant="subtitle2" gutterBottom>
                    Chọn Voucher
                  </Typography>
                  <Select
                    labelId="voucher-select-label"
                    name="externalPrizeId"
                    value={addFormData.externalPrizeId || ""}
                    onChange={handleAddFormChange}
                    label="Chọn Voucher"
                    disabled={vouchers.length === 0}
                  >
                    {vouchers.length === 0 ? (
                      <MenuItem value="" disabled>
                        <em>Không có voucher nào khả dụng</em>
                      </MenuItem>
                    ) : (
                      vouchers.map((voucher) => (
                        <MenuItem key={voucher.voucherId} value={voucher.voucherId}>
                          {voucher.voucherName}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              )}
            {addFormData.type === PrizeType.Physical &&
              addFormData.category === PRIZE_CATEGORY.Product && (
                <FormControl fullWidth margin="normal">
                  <Typography variant="subtitle2" gutterBottom>
                    Chọn sản phẩm
                  </Typography>
                  <Select
                    labelId="product-select-label"
                    name="externalPrizeId"
                    value={addFormData.externalPrizeId || ""}
                    onChange={handleAddFormChange}
                    label="Chọn sản phẩm"
                    disabled={products.length === 0}
                  >
                    {products.length === 0 ? (
                      <MenuItem value="" disabled>
                        <em>Không có sản phẩm nào khả dụng</em>
                      </MenuItem>
                    ) : (
                      products.map((product) => (
                        <MenuItem key={product.itemsId} value={product.itemsId}>
                          {product.itemsName}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              )}
            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Số lượng phát mỗi lần
              </Typography>
              <TextField
                name="quantity"
                type="number"
                value={addFormData.quantity === undefined ? "" : addFormData.quantity}
                onChange={handleAddFormChange}
                fullWidth
                required
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Box>
            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Số lượng tồn kho{" "}
                {addFormData.type === PrizeType.Physical ? (
                  "(Tự động theo số lượng voucher còn lại)"
                ) : (
                  <span style={{ color: "red" }}>*</span>
                )}
              </Typography>
              <TextField
                name="stock"
                type="number"
                value={
                  addFormData.stock === null || addFormData.stock === undefined
                    ? ""
                    : addFormData.stock
                }
                onChange={(e) => {
                  const value = e.target.value;
                  setAddFormData((prev) => ({
                    ...prev,
                    stock: value === "" ? null : Number(value) <= 0 ? 1 : Number(value),
                  }));
                }}
                fullWidth
                InputProps={{ inputProps: { min: 1 } }}
                disabled={addFormData.type === PrizeType.Physical}
                required={addFormData.type !== PrizeType.Physical}
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAddDialog}>Hủy</Button>
          <Button
            onClick={handleSaveNewPrize}
            color="primary"
            variant="contained"
            disabled={isSavingAdd}
          >
            {isSavingAdd ? <CircularProgress size={20} sx={{ color: "white", mr: 1 }} /> : null}
            Thêm mới
          </Button>
        </DialogActions>
      </Dialog>
    </DashboardLayout>
  );
};

export default CampaignDetailPage;
