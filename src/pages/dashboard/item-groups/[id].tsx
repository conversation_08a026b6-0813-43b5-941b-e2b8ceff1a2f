import React, { useEffect, useState } from "react";
import DashboardLayout from "@/src/layouts/dashboard";
import PageTitleWithBackBtn from "@/src/layouts/dashboard/page-title-with-back-btn/page-title";
import {
  Box,
  Button,
  Card,
  Checkbox,
  Divider,
  FormControlLabel,
  FormHelperText,
  IconButton,
  InputAdornment,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { useRouter } from "next/router";
import { Controller, FormProvider, Path, useFieldArray, useForm } from "react-hook-form";
import { fontSize, Grid, Stack } from "@mui/system";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  AddCircle as AddCircleIcon,
} from "@mui/icons-material";
import * as Yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { CreateItemGroupRequestBodyRequest } from "@/src/api/types/item-option-group.type";
import { useItemOptionGroup } from "@/src/api/hooks/item-option-group/use-item-option-group";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useItemOption } from "@/src/api/hooks/item-option/use-item-option";
import { paths } from "@/src/paths";
import toast from "react-hot-toast";
import { ItemOptionCreateDto } from "@/src/api/services/item-option/item-option.service";
import useSnackbar from "@/src/hooks/use-snackbar";

type DefaultValuesType = {
  name?: string;
  require?: boolean;
  isMultiSelect?: boolean;
  options?: {
    itemOptionName?: string;
    itemOptionPrice?: number;
    itemOptionId?: string;
  }[];
};
const validateSchema = Yup.object().shape({
  name: Yup.string().trim().required("Tên là bắt buộc"),
  require: Yup.boolean(),
  isMultiSelect: Yup.boolean(),
  itemOptionName: Yup.string(),
  itemOptionPrice: Yup.number(),
});

export default function NewItemGroup() {
  const [localSelectedOptionGroups, setLocalSelectedOptionGroups] = useState([]);
  const storeId = useStoreId();
  const [initialOptions, setInitialOptions] = useState<DefaultValuesType["options"]>([]);
  const { detailItemOptionGroup, updateItemOptionGroup } = useItemOptionGroup();
  const { listItemOptionByGroupId, deleteItemOption, createAndUpdateItemOption } = useItemOption();

  const methods = useForm<DefaultValuesType>({
    resolver: yupResolver(validateSchema),
  });

  const {
    control,
    handleSubmit,
    register,
    setValue,
    watch,
    formState: { errors },
  } = methods;
  const router = useRouter();

  const { fields, append, remove } = useFieldArray({
    control,
    name: "options",
  });

  const options = watch("options");
  const snackbar = useSnackbar();
  const categoryId: string = router.query.id as string;

  const fetchItemOptionGroup = async () => {
    if (categoryId) {
      const res = await detailItemOptionGroup(categoryId);
      setValue("name", res?.data?.name);
      setValue("require", res?.data?.require);
      setValue("isMultiSelect", res?.data?.isMultiSelect);
    }
  };
  const fetchItemOptionByGroupId = async () => {
    if (categoryId) {
      const result = await listItemOptionByGroupId(0, 999, { itemOptionGroupId: categoryId });
      if (result?.data?.data) {
        const formattedOptions = result.data.data.map((item) => ({
          itemOptionName: item?.name || "",
          itemOptionPrice: item?.price?.toString() || "",
          itemOptionId: item?.itemOptionId || "",
          key: Number(Date.now().toString().trim()),
        }));
        setLocalSelectedOptionGroups(formattedOptions);
        setValue("options", formattedOptions, { shouldValidate: true });
        setInitialOptions(formattedOptions);
      }
    }
  };

  useEffect(() => {
    fetchItemOptionGroup();
    fetchItemOptionByGroupId();
  }, [categoryId]);

  const onAddItemOption = () => {
    append({ itemOptionName: "", itemOptionPrice: 0 });
  };

  const removeItemOptionGroup = (index: number) => {
    remove(index);
  };

  const onSubmitForm = async (data) => {
    const filteredOptions = data.options.filter((option) => {
      const hasName = option.itemOptionName !== undefined && option.itemOptionName !== "";
      const hasPrice = option.itemOptionPrice !== undefined && option.itemOptionPrice !== "";
      return hasName || hasPrice;
    });

    const hasPartialData = filteredOptions.some((option) => {
      const hasName = option.itemOptionName !== undefined && option.itemOptionName !== "";
      const hasPrice = option.itemOptionPrice !== undefined && option.itemOptionPrice !== "";
      return (hasName && !hasPrice) || (!hasName && hasPrice);
    });

    if (hasPartialData) {
      toast.error("Vui lòng nhập đầy đủ cả tên và giá!");
      return;
    }

    const removedOptions = initialOptions.filter(
      (initialOption) =>
        initialOption.itemOptionId &&
        !filteredOptions.some((option) => option.itemOptionId === initialOption.itemOptionId)
    );

    for (const option of removedOptions) {
      if (option.itemOptionId) {
        await deleteItemOption(option.itemOptionId);
      }
    }

    const newData = {
      ...data,
      options: filteredOptions,
    };

    const objectItemOptionGroup: CreateItemGroupRequestBodyRequest = {
      shopId: storeId,
      isMultiSelect: newData.isMultiSelect,
      require: newData.require,
      name: newData.name,
      itemOptionGroupId: categoryId,
    };

    const res = await updateItemOptionGroup(categoryId, objectItemOptionGroup);

    if (res?.status === 200 || res?.status === 204) {
      let newArr: ItemOptionCreateDto[] = [];
      newData?.options?.map((item) => {
        const newObj = {
          shopId: storeId,
          price: Number(item?.itemOptionPrice),
          name: item?.itemOptionName,
          itemOptionGroupId: res?.data?.itemOptionGroupId ?? categoryId,
          itemOptionId: item?.itemOptionId,
        };
        newArr.push(newObj);
      });
      await createAndUpdateItemOption(storeId, newArr);
      router.push(paths.itemGroups.list);
      snackbar.success("Cập nhật nhóm tuỳ chọn thành công");
    }
  };

  function formatCurrency(value: string | number) {
    if (value === "" || value === null || value === undefined) return "";
    const number = typeof value === "number" ? value : Number(value.toString().replace(/,/g, ""));
    if (isNaN(number)) return "";
    return number.toLocaleString("vi-VN");
  }

  function parseCurrency(value: string) {
    return value.replace(/,/g, "");
  }

  const handleCancel = () => {
    router.back();
  };

  return (
    <DashboardLayout>
      <Box sx={{ p: 4 }}>
        <PageTitleWithBackBtn title="Sửa nhóm tùy chọn" backPath={paths.itemGroups.list} />
        <>
          <FormProvider {...methods}>
            <Grid container spacing={2}>
              {/* Cột chiếm 8 phần */}
              <Grid size={{ xs: 12, md: 8 }}>
                <Card sx={{ p: 2 }}>
                  <Box>
                    <Typography variant="subtitle1" gutterBottom sx={{ display: "flex" }}>
                      Tên nhóm tùy chọn{" "}
                      <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
                    </Typography>
                    <Controller
                      name="name"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          error={!!errors.name}
                          helperText={errors.name?.message as string}
                          variant="outlined"
                        />
                      )}
                    />
                  </Box>
                  <Box>
                    <Controller
                      name="require"
                      control={control}
                      render={({ field }) => {
                        return (
                          <div>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  {...field}
                                  checked={!!field?.value}
                                  onChange={(e) => field.onChange(e.target.checked)}
                                />
                              }
                              label="Bắt buộc chọn"
                            />
                            {errors.require && (
                              <FormHelperText style={{ color: "red" }}>
                                {errors.require.message}
                              </FormHelperText>
                            )}
                          </div>
                        );
                      }}
                    />
                  </Box>
                  <Box>
                    <Controller
                      name="isMultiSelect"
                      control={control}
                      render={({ field }) => (
                        <div>
                          <FormControlLabel
                            control={
                              <Checkbox
                                {...field}
                                checked={!!field?.value}
                                onChange={(e) => field.onChange(e.target.checked)}
                              />
                            }
                            label="Lựa chọn nhiều tùy chọn"
                          />
                          {errors.isMultiSelect && (
                            <FormHelperText style={{ color: "red" }}>
                              {errors.isMultiSelect.message}
                            </FormHelperText>
                          )}
                        </div>
                      )}
                    />
                  </Box>
                </Card>
              </Grid>
            </Grid>

            <Stack spacing={2} marginTop={"35px"}>
              {fields?.map((item, index) => (
                <Stack spacing={1} key={item.id}>
                  <Box
                    sx={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr auto",
                      gap: 1,
                      alignItems: "center",
                    }}
                  >
                    <Box>
                      <Controller
                        name={`options.${index}.itemOptionName` as Path<DefaultValuesType>} // Unique field name
                        control={control}
                        render={({ field, formState, fieldState }) => {
                          return (
                            <TextField
                              {...field}
                              required
                              fullWidth
                              variant="outlined"
                              placeholder="Tên tuỳ chọn"
                            />
                          );
                        }}
                      />
                    </Box>
                    <Box>
                      <Controller
                        name={`options.${index}.itemOptionPrice` as Path<DefaultValuesType>}
                        control={control}
                        render={({ field }) => {
                          const formattedValue =
                            typeof field.value === "string" || typeof field.value === "number"
                              ? formatCurrency(field.value)
                              : "";
                          return (
                            <TextField
                              {...field}
                              fullWidth
                              required
                              type="text"
                              value={formattedValue}
                              onChange={(e) => {
                                const rawValue = e.target.value;
                                const numericValue = parseCurrency(rawValue);

                                if (numericValue === "" || Number(numericValue) < 0) {
                                  field.onChange(0);
                                } else {
                                  field.onChange(Number(numericValue));
                                }
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "-" || e.key === "e" || e.key === "E") {
                                  e.preventDefault();
                                }
                              }}
                              onWheel={(e) => e.target instanceof HTMLElement && e.target.blur()}
                              variant="outlined"
                              placeholder="Giá"
                              InputProps={{
                                endAdornment: <InputAdornment position="end">đ</InputAdornment>,
                              }}
                            />
                          );
                        }}
                      />
                    </Box>

                    <Box sx={{ mt: "2px" }}>
                      <Tooltip title="Xóa" arrow placement="top">
                        <IconButton
                          onClick={() => removeItemOptionGroup(index)}
                          size="small"
                          sx={{
                            bgcolor: "error.lighter",
                            color: "error.main",
                            "&:hover": { bgcolor: "error.light" },
                            width: 32,
                            height: 32,
                          }}
                        >
                          <DeleteIcon fontSize="medium" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                </Stack>
              ))}
              <Button
                startIcon={<AddCircleIcon />}
                onClick={onAddItemOption}
                variant="outlined"
                disabled={fields.length >= 10}
                sx={{
                  borderStyle: "dashed",
                  height: 40,
                  color: "#2654FE",
                  borderColor: "#2654FE",
                  "&:hover": {
                    borderStyle: "dashed",
                    bgcolor: "primary.lighter",
                  },
                }}
              >
                Thêm tùy chọn ({fields.length}/10)
              </Button>
            </Stack>
            <Divider />
            <Grid container sx={{ mt: 3 }}>
              <Box display="flex" gap={1}>
                <Button
                  sx={{ color: "#2654FE", borderColor: "#2654FE" }}
                  variant="outlined"
                  onClick={handleCancel}
                >
                  Hủy bỏ
                </Button>
                <Button
                  sx={{ background: "#2654FE" }}
                  onClick={handleSubmit(onSubmitForm)}
                  variant="contained"
                  color="primary"
                >
                  Lưu
                </Button>
              </Box>
            </Grid>
          </FormProvider>
        </>
      </Box>
    </DashboardLayout>
  );
}
