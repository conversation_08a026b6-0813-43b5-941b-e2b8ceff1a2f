import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  IconButton,
  Box,
  Typography,
  Select,
  MenuItem,
  Paper,
  TextField,
  InputAdornment,
  Button,
  Switch,
  Modal,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Stack,
  Input,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Tooltip,
} from "@mui/material";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
  Search as SearchIcon,
  FileDownload as FileDownloadIcon,
  Label,
} from "@mui/icons-material";
import { on } from "events";
import { paths } from "@/src/paths";
import { useCampaign } from "@/src/api/hooks/campaign/campaign";
import { useStoreId } from "@/src/hooks/use-store-id";
import { CampaignDto, CampaignQueryParams } from "@/src/api/services/campaign/campaign.service";
import dayjs from "dayjs";
import ModalCreateCampaignV2 from "./component/modal-create-campaign";
import ModalUpdateCampaignV2 from "./component/modal-update-campaign";
import useSnackbar from "@/src/hooks/use-snackbar";
import ModalDetailCampaignV2 from "./component/modal-detail-campaign";
import { useDebounce } from "@/src/hooks/use-debounce";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
interface StatusConfig {
  label: string;
  color: string;
  backgroundColor: string;
}

const getStatusConfig = (status: string): StatusConfig => {
  switch (status) {
    case "Draft":
      return {
        label: "Bản nháp",
        color: "#637381",
        backgroundColor: "#F4F6F8",
      };
    case "Scheduled":
      return {
        label: "Đã lên lịch",
        color: "#B76E00",
        backgroundColor: "#FFF5E5",
      };
    case "Running":
      return {
        label: "Đang chạy",
        color: "#118D57",
        backgroundColor: "#E9FCD4",
      };
    case "Paused":
      return {
        label: "Tạm dừng",
        color: "#B76E00",
        backgroundColor: "#FFF5E5",
      };
    case "OutOfBudget":
      return {
        label: "Hết ngân sách",
        color: "#B72136",
        backgroundColor: "#FFE7D9",
      };
    case "Completed":
      return {
        label: "Đã hoàn tất",
        color: "#1890FF",
        backgroundColor: "#E6F7FF",
      };
    case "Deleted":
      return {
        label: "Đã xoá",
        color: "#B72136",
        backgroundColor: "#FFE7D9",
      };
    default:
      return {
        label: status,
        color: "#637381",
        backgroundColor: "#F4F6F8",
      };
  }
};

type CampaignStatus =
  | "Draft"
  | "Scheduled"
  | "Running"
  | "Paused"
  | "OutOfBudget"
  | "Completed"
  | "Deleted";

const campaignStatusOptions = [
  { value: "", label: "Tất cả" },
  { value: "Draft", label: "Bản nháp" },
  { value: "Scheduled", label: "Đã lên lịch" },
  { value: "Running", label: "Đang chạy" },
  // { value: "Paused", label: "Tạm dừng thủ công" },
  { value: "OutOfBudget", label: "Hết ngân sách" },
  { value: "Completed", label: "Đã hoàn tất" },
  // { value: "Deleted", label: "Đã xoá" },
];

export default function Campaign() {
  const pathname = usePathname();
  const shopId = useStoreId();
  const { getListCampaign, deleteCampaign } = useCampaign();
  const [rows, setRows] = useState<CampaignDto[]>([]);
  const [selected, setSelected] = useState<number[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [isOpenModalCreate, setIsOpenModalCreate] = useState<boolean>(false);
  const [campaignType, setCampaignType] = useState("oneTime");
  const [isDetail, setIsDetail] = useState(false);
  const [isOpenModalDelete, setIsOpenModalDelete] = useState(false);
  const [isOpenModalUpdate, setIsOpenModalUpdate] = useState(false);
  const [selectedCampaignEdit, setSelectedCampaignEdit] = useState<CampaignDto>(null);
  const [selectedCampaignDelete, setSelectedCampaignDelete] = useState<CampaignDto>(null);
  const [selectedCampaignDetail, setSelectedCampaignDetail] = useState<CampaignDto>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [searchValue, setSearchValue] = useState("");
  const [statusFilter, setStatusFilter] = useState<CampaignStatus | "">("");

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const snackbar = useSnackbar();
  // const fetchListCampaign = async () => {
  //   const data: CampaignQueryParams = {
  //     Status: statusFilter,
  //     shopId: shopId,
  //     PageIndex: page,
  //     PageSize: rowsPerPage,
  //   };
  //   const res = await getListCampaign(data);
  //   console.log({ res });
  //   if (res && res?.status === 200) {
  //     if (Array.isArray(res?.data?.data?.result) && res?.data?.data?.result.length > 0) {
  //       setRows(res?.data?.data.result);
  //       setTotalCount(res?.data?.data?.total);
  //     }
  //   }
  // };
  // useEffect(() => {
  //   fetchListCampaign();
  // }, [shopId, page, rowsPerPage]);

  const handleSelectAllClick = (event) => {
    // if (event.target.checked) {
    //   const newSelected = rows.map((n) => n.id);
    //   setSelected(newSelected);
    //   return;
    // }
    // setSelected([]);
  };

  const handleClick = (id: number) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected: number[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
    }
    setSelected(newSelected);
  };

  const isSelected = (id: number) => selected.indexOf(id) !== -1;

  const handleChangePage = (event, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  const searchFieldProps = {
    placeholder: "Tìm kiếm",
    variant: "outlined" as "outlined",
    size: "small" as "small",
    sx: {
      width: { xs: "100%", sm: 240 },
      "& .MuiOutlinedInput-root": {
        borderRadius: 30,
        backgroundColor: "#fff",
      },
    },
    InputProps: {
      startAdornment: (
        <InputAdornment position="start">
          <SearchIcon fontSize="small" />
        </InputAdornment>
      ),
    },
  };

  const importButtonProps = {
    variant: "outlined" as "outlined",
    startIcon: <FileDownloadIcon />,
    sx: {
      textTransform: "none",
      width: { xs: "100%", sm: "auto" },
      border: "none",
      boxShadow: "none",
    },
    children: "Import",
  };

  const addButtonProps = {
    variant: "contained" as "contained",
    // sx: {
    //     borderRadius: 1,
    //     bgcolor: "#2654FE",
    //     textTransform: "none",
    //     "&:hover": { bgcolor: "#1765cc" },
    //     width: { xs: "100%", sm: "auto" },
    // },
    children: "Tạo chiến dịch",
    onClick: () => {
      setSelectedCampaignEdit(null);
      setIsOpenModalCreate(true);
    },
  };
  const addButtonDisableProps = {
    variant: "contained" as "contained",
    // sx: {
    //     borderRadius: 1,
    //     bgcolor: "#2654FE",
    //     textTransform: "none",
    //     "&:hover": { bgcolor: "#1765cc" },
    //     width: { xs: "100%", sm: "auto" },
    // },
    children: "Tạo chiến dịch",
  };

  const tableContainerProps = {
    sx: {
      overflowX: "auto",
      maxWidth: "100%",
    },
  };

  const paginationBoxProps = {
    sx: {
      display: "flex",
      justifyContent: "flex-end",
      alignItems: "center",
      px: 2,
      py: 1.5,
      flexDirection: { xs: "column", sm: "row" },
      gap: 1,
    },
  };

  const handleDeleteCampaign = async () => {
    const res = await deleteCampaign(shopId, selectedCampaignDelete?.campaignId);
    if (res && res?.status === 200) {
      fetchCampaigns();
      setIsOpenModalDelete(false);
      snackbar.success("Xoá chiến dịch thành công");
      setSelectedCampaignDelete(null);
    }
  };

  const debouncedSearchValue = useDebounce(searchValue, 500);
  const fetchCampaigns = async () => {
    const data: CampaignQueryParams = {
      Status: statusFilter,
      shopId: shopId,
      Paging: {
        PageIndex: page,
        PageSize: rowsPerPage,
        Search: debouncedSearchValue,
      },
    };

    const res = await getListCampaign(data);
    if (res && res?.status === 200) {
      if (Array.isArray(res?.data?.data?.result) && res?.data?.data?.result.length > 0) {
        setRows(res?.data?.data.result);
        setTotalCount(res?.data?.data?.total);
      } else {
        setRows([]);
        setTotalCount(0);
      }
    }
  };
  useEffect(() => {
    fetchCampaigns();
  }, [shopId, page, rowsPerPage, debouncedSearchValue, statusFilter]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPage(0);
    setSearchValue(event.target.value);
  };

  const tableCellBaseStyles = {
    fontSize: { xs: "12px", md: "14px" },
  };

  const tableHeaderCellStyles = {
    ...tableCellBaseStyles,
    fontWeight: 600,
  };

  const actionButtonStyles = {
    padding: { xs: "6px", md: "8px" },
    "&.Mui-disabled": {
      color: "rgba(0, 0, 0, 0.26)",
    },
  };

  const statusBadgeStyles = {
    display: "inline-block",
    px: { xs: 1, md: 1.5 },
    py: 0.5,
    borderRadius: 1,
    fontWeight: 500,
    fontSize: { xs: 11, md: 13 },
    whiteSpace: "nowrap",
  };

  return (
    <>
      {!isDetail && (
        <>
          <Box
            sx={{
              p: { xs: 1, md: 2 },
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
              marginBottom: 2,
              flexDirection: { xs: "column", sm: "row" },
              gap: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                marginBottom: { xs: 1, md: 2 },
                width: "100%",
                flexDirection: { xs: "column", sm: "row" },
                gap: 2,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  flexDirection: { xs: "column", sm: "row" },
                  gap: { xs: 1, sm: 2 },
                  width: { xs: "100%", sm: "auto" },
                }}
              >
                <TextField
                  placeholder="Nhập tên chiến dịch"
                  variant="outlined"
                  size="small"
                  value={searchValue}
                  onChange={handleSearch}
                  sx={{
                    width: { xs: "100%", sm: 300 },
                    "& .MuiOutlinedInput-root": {
                      borderRadius: 1,
                      fontSize: { xs: "14px", md: "16px" },
                    },
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon fontSize="small" />
                      </InputAdornment>
                    ),
                  }}
                />
                <Select
                  value={statusFilter}
                  onChange={(e) => {
                    setPage(0);
                    setStatusFilter(e.target.value as CampaignStatus);
                  }}
                  size="small"
                  sx={{
                    width: { xs: "100%", sm: 200 },
                    "& .MuiOutlinedInput-root": {
                      borderRadius: 1,
                      fontSize: { xs: "14px", md: "16px" },
                    },
                    marginLeft: { xs: 0, sm: 2 },
                  }}
                  displayEmpty
                >
                  {campaignStatusOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </Box>

              {isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? (
                <Button
                  {...addButtonProps}
                  sx={{
                    textTransform: "none",
                    width: { xs: "100%", sm: "auto" },
                    fontSize: { xs: "14px", md: "16px" },
                    padding: { xs: "8px 16px", md: "6px 12px" },
                  }}
                />
              ) : (
                <Tooltip title="Bạn không có quyền thêm chiến dịch">
                  <span style={{ width: "100%" }}>
                    <Button
                      {...addButtonDisableProps}
                      disabled={true}
                      sx={{
                        textTransform: "none",
                        width: { xs: "100%", sm: "auto" },
                        fontSize: { xs: "14px", md: "16px" },
                        padding: { xs: "8px 16px", md: "6px 12px" },
                      }}
                    />
                  </span>
                </Tooltip>
              )}
            </Box>
          </Box>

          <Paper elevation={3} sx={{ borderRadius: 2, overflow: "hidden" }}>
            <Box sx={{ overflowX: "auto" }}>
              <Table stickyHeader sx={{ minWidth: 800 }}>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ ...tableHeaderCellStyles, minWidth: 50 }}>STT</TableCell>
                    <TableCell sx={{ ...tableHeaderCellStyles, minWidth: 200 }}>
                      Tên chiến dịch
                    </TableCell>
                    <TableCell sx={{ ...tableHeaderCellStyles, minWidth: 120 }}>Loại tin</TableCell>
                    <TableCell sx={{ ...tableHeaderCellStyles, minWidth: 120 }}>
                      Trạng thái
                    </TableCell>
                    <TableCell sx={{ ...tableHeaderCellStyles, minWidth: 160 }}>Ngày tạo</TableCell>
                    <TableCell align="center" sx={{ ...tableHeaderCellStyles, minWidth: 120 }}>
                      Quản lý
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Array.isArray(rows) &&
                    rows.map((row, index) => {
                      const isItemSelected = isSelected(index);
                      return (
                        <TableRow
                          hover
                          onClick={() => handleClick(index)}
                          role="checkbox"
                          aria-checked={isItemSelected}
                          tabIndex={-1}
                          key={row.campaignId}
                          selected={isItemSelected}
                        >
                          <TableCell sx={tableCellBaseStyles}>
                            {page * rowsPerPage + index + 1}
                          </TableCell>
                          <TableCell
                            component="th"
                            scope="row"
                            onClick={() => {
                              setSelectedCampaignDetail(row);
                              setIsDetail(true);
                            }}
                            sx={{
                              ...tableCellBaseStyles,
                              fontWeight: 600,
                              color: "primary.main",
                              cursor: "pointer",
                              "&:hover": {
                                textDecoration: "underline",
                                color: "primary.main",
                              },
                            }}
                          >
                            <TruncatedText text={row.campaignName} />
                          </TableCell>
                          <TableCell sx={tableCellBaseStyles}>{row.templateType}</TableCell>
                          <TableCell>
                            <Box
                              sx={{
                                ...statusBadgeStyles,
                                color: getStatusConfig(row.status).color,
                                backgroundColor: getStatusConfig(row.status).backgroundColor,
                              }}
                            >
                              {getStatusConfig(row.status).label}
                            </Box>
                          </TableCell>
                          <TableCell sx={{ ...tableCellBaseStyles, whiteSpace: "nowrap" }}>
                            {dayjs(row.createdDate).format("DD/MM/YYYY HH:mm:ss")}
                          </TableCell>
                          <TableCell align="center">
                            <Box sx={{ display: "flex", justifyContent: "center", gap: 0.5 }}>
                              <Tooltip
                                title={
                                  !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                                    ? "Bạn không có quyền cập nhật"
                                    : "Cập nhật chiến dịch"
                                }
                              >
                                <span>
                                  <IconButton
                                    disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                                    size="small"
                                    sx={{
                                      ...actionButtonStyles,
                                      color: "#1a73e8",
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedCampaignEdit(row);
                                      setIsOpenModalUpdate(true);
                                    }}
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                </span>
                              </Tooltip>

                              <Tooltip
                                title={
                                  !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)
                                    ? "Bạn không có quyền xoá"
                                    : row.status === "Running"
                                    ? "Không thể xóa chiến dịch đang chạy"
                                    : row.status === "Completed"
                                    ? "Không thể xóa chiến dịch đã hoàn tất"
                                    : "Xoá chiến dịch"
                                }
                              >
                                <span>
                                  <IconButton
                                    disabled={
                                      !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete) ||
                                      row.status === "Running" ||
                                      row.status === "Completed"
                                    }
                                    size="small"
                                    sx={{
                                      ...actionButtonStyles,
                                      color: "#f44336",
                                    }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedCampaignDelete(row);
                                      setIsOpenModalDelete(true);
                                    }}
                                  >
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </span>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  {rows.length <= 0 && (
                    <TableRow>
                      <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                        <Typography variant="body2" color="text.secondary" sx={tableCellBaseStyles}>
                          Không có dữ liệu
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </Box>
          </Paper>

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              alignItems: "center",
              px: { xs: 1, md: 2 },
              py: 1.5,
              flexDirection: { xs: "column", sm: "row" },
              gap: { xs: 2, sm: 1 },
              mt: 2,
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                order: { xs: 2, sm: 1 },
              }}
            >
              <Typography variant="body2" sx={{ fontSize: { xs: "12px", md: "14px" } }}>
                Số dòng mỗi trang
              </Typography>
              <Select
                value={rowsPerPage}
                onChange={handleChangeRowsPerPage}
                size="small"
                sx={{
                  minWidth: 60,
                  "& .MuiOutlinedInput-notchedOutline": { border: "none" },
                  "& .MuiSelect-select": {
                    padding: "4px 8px",
                    fontSize: { xs: "12px", md: "14px" },
                  },
                }}
              >
                <MenuItem value={10}>10</MenuItem>
                <MenuItem value={25}>25</MenuItem>
                <MenuItem value={50}>50</MenuItem>
              </Select>
              <Typography variant="body2" sx={{ fontSize: { xs: "12px", md: "14px" } }}>
                {`${page * rowsPerPage + 1}–${Math.min(
                  (page + 1) * rowsPerPage,
                  rows?.length
                )} của ${totalCount}`}
              </Typography>
            </Box>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                order: { xs: 1, sm: 2 },
              }}
            >
              <IconButton
                disabled={page === 0}
                onClick={() => handleChangePage(null, page - 1)}
                size="small"
                sx={{ padding: { xs: "6px", md: "8px" } }}
              >
                <NavigateBeforeIcon fontSize="small" />
              </IconButton>
              <IconButton
                disabled={page >= Math.ceil(totalCount / rowsPerPage) - 1}
                onClick={() => handleChangePage(null, page + 1)}
                size="small"
                sx={{ padding: { xs: "6px", md: "8px" } }}
              >
                <NavigateNextIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>

          <Dialog
            open={isOpenModalDelete}
            onClose={() => setIsOpenModalDelete(false)}
            maxWidth="sm"
            fullWidth
            PaperProps={{
              sx: {
                margin: { xs: 1, sm: 3 },
                maxHeight: { xs: "calc(100% - 32px)", sm: "calc(100% - 96px)" },
              },
            }}
          >
            <DialogTitle sx={{ fontSize: { xs: "18px", md: "20px" } }}>Xoá chiến dịch</DialogTitle>
            <DialogContent>
              <DialogContentText sx={{ fontSize: { xs: "14px", md: "16px" } }}>
                Bạn có chắc chắn muốn xoá chiến dịch này?
              </DialogContentText>
            </DialogContent>
            <DialogActions sx={{ padding: { xs: "16px 24px 24px", md: "8px 24px 24px" } }}>
              <Button
                onClick={() => setIsOpenModalDelete(false)}
                variant="outlined"
                sx={{ fontSize: { xs: "14px", md: "16px" } }}
              >
                Huỷ
              </Button>
              <Button
                onClick={() => handleDeleteCampaign()}
                color="error"
                variant="contained"
                autoFocus
                sx={{ fontSize: { xs: "14px", md: "16px" } }}
              >
                Xoá
              </Button>
            </DialogActions>
          </Dialog>
        </>
      )}

      <ModalCreateCampaignV2
        setCampaignType={setCampaignType}
        campaignType={campaignType}
        open={isOpenModalCreate}
        setOpen={setIsOpenModalCreate}
        fetchListCampaign={fetchCampaigns}
      />
      {isDetail && (
        <ModalDetailCampaignV2
          open={isDetail}
          setOpen={setIsDetail}
          campaignEdit={selectedCampaignDetail}
          fetchListCampaign={fetchCampaigns}
        />
      )}
      {isOpenModalUpdate && (
        <ModalUpdateCampaignV2
          open={isOpenModalUpdate}
          setOpen={setIsOpenModalUpdate}
          campaignEdit={selectedCampaignEdit}
          fetchListCampaign={fetchCampaigns}
        />
      )}
    </>
  );
}
