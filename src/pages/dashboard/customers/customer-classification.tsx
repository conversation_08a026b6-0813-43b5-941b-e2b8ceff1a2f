import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  IconButton,
  Box,
  Typography,
  Select,
  MenuItem,
  Paper,
  TextField,
  InputAdornment,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TablePagination,
  Tooltip,
} from "@mui/material";
import DashboardLayout from "../../../layouts/dashboard";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
  Search as SearchIcon,
  FileDownload as FileDownloadIcon,
} from "@mui/icons-material";
import ModalClassificationCustomer from "./components/modal";
import ModalImportExcel from "./components/modal-import-excel";
import ModalListCustomerByGroup from "./components/modal-list-customer-by-group";
import ModalUpdateGroupUser from "./components/modal-update-group-user";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { useUserGroup } from "@/src/api/hooks/user-group/use-user-group";
import { useStoreId } from "@/src/hooks/use-store-id";
import dayjs from "dayjs";
import { UserGroupQueryParams } from "@/src/api/services/user-group/user-group.service";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import _ from "lodash";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { usePathname } from "next/navigation";
import ActionButton from "@/src/components/common/ActionButton";
import TruncatedText from "@/src/components/truncated-text/truncated-text";

export interface UserGroupDto {
  id: string;
  partnerId: string;
  shopId: string;
  groupName: string;
  description: string;
  conditions: any | null;
  isAuto: boolean;
  status: "Active" | "Inactive" | string;
  createdDate: string;
  modifiedDate: string | null;
  createdBy: string | null;
  modifiedBy: string | null;
  isDeleted: boolean;
  deletedAt: string | null;
}
export interface UserOfUserGroupDto {
  groupId: string;
  source: string;
  userId: string;
  referralCode: string;
  fullname: string;
  email: string;
  phoneNumber: string;
}

const getAutoStyle = (isAuto) => {
  if (isAuto) {
    return {
      title: "Tự động",
      color: "#fff8e1",
      textColor: "#f9a825",
    };
  } else {
    return {
      title: "Thủ công",
      color: "#f5f5f5",
      textColor: "#616161",
    };
  }
};

export default function CustomerClassification() {
  const pathname = usePathname();
  const { getListUserGroup, deleteUserGroup } = useUserGroup();
  const [selected, setSelected] = useState<number[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [isOpenModalCreate, setIsOpenModalCreate] = useState<boolean>(false);
  const [isOpenModalEdit, setIsOpenModalEdit] = useState<boolean>(false);
  const [isOpenModalDeleteMany, setIsOpenModalDeleteMany] = useState<boolean>(false);
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [isOpenModalImport, setIsOpenModalImport] = useState<boolean>(false);
  const [itemIdToDelete, setItemIdToDelete] = useState<string | null>(null);
  const [itemIdToUpdate, setItemIdToUpdate] = useState<string | null>(null);

  const [isOpenModalListCustomer, setIsOpenModalListCustomer] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [listUserGroup, setListUserGroup] = useState<UserGroupDto[]>([]);
  const [totalCount, setTotalCount] = useState<number>();
  const shopId = useStoreId();
  const [searchText, setSearchText] = useState<string>("");
  const [selectedUserGroup, setSelectedUserGroup] = useState<UserGroupDto>();

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const fetchListUserGroup = async (
    currentPage,
    pageSize,
    searchQuery,
    shopId,
    isOpenModalImport
  ) => {
    const skip = currentPage * pageSize;
    const limit = pageSize;
    const data: UserGroupQueryParams = {
      ShopId: shopId,
      GroupName: searchQuery,
      Paging: {
        PageSize: pageSize,
        PageIndex: currentPage,
      },
    };
    const response = await getListUserGroup(data);

    if (response && response?.status === 200) {
      setListUserGroup(response?.data.result);
      setTotalCount(response.data.total);
    }
  };

  // Wrap fetchListUserGroup with debounce
  const debouncedFetchUserList = useCallback(
    _.debounce(
      (currentPage, pageSize, searchQuery, shopId, isOpenModalImport, isOpenModalCreate) => {
        fetchListUserGroup(currentPage, pageSize, searchQuery, shopId, isOpenModalImport);
      },
      400
    ), // Delay 1s
    []
  );

  useEffect(() => {
    debouncedFetchUserList(
      page,
      rowsPerPage,
      searchText,
      shopId,
      isOpenModalImport,
      isOpenModalCreate
    );
    return () => {
      debouncedFetchUserList.cancel();
    };
  }, [
    page,
    rowsPerPage,
    searchText,
    shopId,
    debouncedFetchUserList,
    isOpenModalImport,
    isOpenModalCreate,
    isOpenModalDelete,
    isOpenModalEdit,
  ]);

  const handleClick = (id: number) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected: number[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
    }
    setSelected(newSelected);
  };

  const isSelected = (id: number) => selected.indexOf(id) !== -1;

  const handleChangePage = (event, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const searchFieldProps = {
    placeholder: "Tìm kiếm",
    variant: "outlined" as "outlined",
    size: "small" as "small",
    sx: {
      width: { xs: "100%", sm: 240 },
      "& .MuiOutlinedInput-root": {
        borderRadius: 60,
        backgroundColor: "#fff",
      },
    },
    InputProps: {
      startAdornment: (
        <InputAdornment position="start">
          <SearchIcon fontSize="small" />
        </InputAdornment>
      ),
    },
  };

  const importButtonProps = {
    variant: "outlined" as "outlined",
    startIcon: <FileDownloadIcon />,
    sx: {
      textTransform: "none",
      width: { xs: "100%", sm: "auto" },
      boxShadow: "none",
    },
    children: "Import",
  };

  const addButtonProps = {
    variant: "contained" as "contained",
    sx: {
      borderRadius: 1,
      bgcolor: "#2654FE",
      textTransform: "none",
      "&:hover": { bgcolor: "#1765cc" },
      width: { xs: "100%", sm: "auto" },
    },
    children: "Thêm nhóm",
    onClick: handleOpenDialog,
  };

  const addButtonDisableProps = {
    variant: "contained" as "contained",
    sx: {
      borderRadius: 1,
      textTransform: "none",
      width: { xs: "100%", sm: "auto" },
    },
    children: "Thêm nhóm",
  };

  const tableContainerProps = {
    sx: {
      overflowX: "auto",
      maxWidth: "100%",
      px: 2,
    },
  };

  const paginationBoxProps = {
    sx: {
      display: "flex",
      justifyContent: "flex-end",
      alignItems: "center",
      px: 2,
      py: 1.5,
      flexDirection: { xs: "column", sm: "row" },
      gap: 1,
    },
  };
  const confirmDeleteMany = () => {
    // const newRows = rows.filter((row) => !selected.includes(row.id));
    // setRows(newRows);
    // setSelected([]);
    // setIsOpenModalDeleteMany(false);
  };
  const confirmDelete = async () => {
    const res = await deleteUserGroup(shopId, itemIdToDelete);
    if (res && res?.status === 200) {
      setIsOpenModalDelete(false);
    }
  };

  return (
    <DashboardLayout>
      <Box padding={{ xs: 2, sm: 3 }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
          <Box sx={{}}>
            <TitleTypography
              sx={{
                textTransform: "none",
                color: " #000 !important",
                fontSize: "20px !important",
                fontWeight: "700",
                lineHeight: "20px",
              }}
            >
              Phân loại khách hàng
            </TitleTypography>
          </Box>
          <Box
            sx={{
              // paddingBottom: 3,
              // paddingTop: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-end",
              flexDirection: { xs: "column", sm: "row" },
              gap: 2,
              background: "#f5f6fa",
            }}
          ></Box>
        </Box>
        <Paper elevation={3} sx={{ borderRadius: 1, mb: 3 }}>
          <Box
            sx={{
              background: "#fff",
              p: 2,
              mb: 2,
              borderRadius: 1,
            }}
          >
            <Box
              sx={{
                display: "flex",
                flexDirection: "row",
                gap: 2,
                justifyContent: { xs: "space-between", sm: "flex-end" },
                alignItems: "center",
              }}
            >
              {selected.length > 0 && isGranted(pathname, PERMISSION_TYPE_ENUM.Delete) && (
                <Box sx={{ width: { xs: "100%", sm: "auto" } }}>
                  <ActionButton
                    permission={PERMISSION_TYPE_ENUM.Delete}
                    tooltip={selected.length === 0 ? "Chọn nhóm để xoá" : ""}
                    isGranted={isGranted}
                    pathname={pathname}
                    onClick={() => setIsOpenModalDeleteMany(true)}
                    variant="outlined"
                    color="error"
                    disabled={selected.length === 0}
                    sx={{
                      textTransform: "none",
                      width: "100%",
                      minWidth: 0,
                      boxShadow: "none",
                      height: "45px",
                    }}
                    size="medium"
                  >
                    Xóa ({selected.length})
                  </ActionButton>
                </Box>
              )}
              <Box sx={{ width: { xs: "100%", sm: "auto" } }}>
                <ActionButton
                  permission={PERMISSION_TYPE_ENUM.Import}
                  tooltip="Bạn không có quyền import"
                  isGranted={isGranted}
                  pathname={pathname}
                  onClick={() => setIsOpenModalImport(true)}
                  variant="outlined"
                  startIcon={<FileDownloadIcon />}
                  sx={{
                    textTransform: "none",
                    width: "100%",
                    minWidth: 0,
                    boxShadow: "none",
                    height: "45px",
                  }}
                  size="medium"
                >
                  Import
                </ActionButton>
              </Box>
              <Box sx={{ width: { xs: "100%", sm: "auto" } }}>
                <ActionButton
                  permission={PERMISSION_TYPE_ENUM.Add}
                  tooltip="Bạn không có quyền thêm nhóm khách hàng"
                  isGranted={isGranted}
                  pathname={pathname}
                  onClick={() => setIsOpenModalCreate(true)}
                  variant="contained"
                  sx={{
                    borderRadius: 1,
                    bgcolor: "#2654FE",
                    textTransform: "none",
                    "&:hover": { bgcolor: "#1765cc" },
                    width: "100%",
                    minWidth: 0,
                    height: "45px",
                  }}
                  size="medium"
                >
                  Thêm nhóm
                </ActionButton>
              </Box>
            </Box>
          </Box>
          <TableContainer {...tableContainerProps}>
            <Table stickyHeader sx={{ width: "max-content", minWidth: "100%" }}>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ width: "30px" }}>STT</TableCell>
                  <TableCell>Tên nhóm</TableCell>
                  <TableCell>Loại nhóm</TableCell>
                  <TableCell>Ngày thêm</TableCell>
                  <TableCell>Giới thiệu</TableCell>
                  <TableCell align="center">Quản lý</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {Array.isArray(listUserGroup) &&
                  listUserGroup.length > 0 &&
                  listUserGroup.map((row, index) => {
                    return (
                      <TableRow key={row.id}>
                        <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                        <TableCell
                          onClick={() => {
                            setIsOpenModalListCustomer(true);
                            setSelectedUserGroup(row);
                          }}
                          component="th"
                          scope="row"
                          sx={{
                            width: 300,
                            color: "#1a73e8",
                            "&:hover": {
                              textDecoration: "underline",
                              color: "#1a73e8",
                            },
                          }}
                        >
                          <TruncatedText
                            text={row.groupName}
                            typographyProps={{ width: 300, fontSize: 15 }}
                            isLink={true}
                          />
                        </TableCell>
                        <TableCell>
                          {(() => {
                            const { title, color, textColor } = getAutoStyle(row.isAuto);
                            return (
                              <Typography
                                sx={{
                                  textAlign: "center",
                                  padding: "4px 0",
                                  borderRadius: 3,
                                  width: "70%",
                                  backgroundColor: color,
                                  color: textColor,
                                  fontWeight: 500,
                                  fontSize: "14px",
                                }}
                              >
                                {title}
                              </Typography>
                            );
                          })()}
                        </TableCell>

                        <TableCell>{dayjs(row.createdDate).format("DD/MM/YYYY")}</TableCell>
                        <TableCell>
                          <TruncatedText text={row.description} typographyProps={{ width: 500 }} />
                        </TableCell>
                        <TableCell align="center">
                          <Tooltip
                            title={
                              !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                                ? "Bạn không có quyền sửa"
                                : ""
                            }
                          >
                            <span>
                              <IconButton
                                size="small"
                                sx={{ color: "#1a73e8" }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setIsOpenModalEdit(true);
                                  setItemIdToUpdate(row.id);
                                }}
                                disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </span>
                          </Tooltip>
                          <Tooltip
                            title={
                              !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)
                                ? "Bạn không có quyền xoá"
                                : ""
                            }
                          >
                            <span>
                              <IconButton
                                size="small"
                                sx={{ color: "#f44336" }}
                                onClick={(e) => {
                                  console.log({ row });
                                  e.stopPropagation();
                                  setIsOpenModalDelete(true);
                                  setItemIdToDelete(row.id);
                                }}
                                disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </span>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    );
                  })}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
        <TablePagination
          sx={{ marginRight: 2 }}
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={rowPerPageOptionsDefault}
          labelRowsPerPage="Số dòng mỗi trang"
        />

        <ModalClassificationCustomer
          isOpenModalCreate={isOpenModalCreate}
          setIsOpenModalCreate={setIsOpenModalCreate}
        />
        <ModalImportExcel open={isOpenModalImport} setOpen={setIsOpenModalImport} />
        <ModalListCustomerByGroup
          open={isOpenModalListCustomer}
          setOpen={setIsOpenModalListCustomer}
          selectedUserGroup={selectedUserGroup}
        />
        <ModalUpdateGroupUser
          open={isOpenModalEdit}
          setOpen={setIsOpenModalEdit}
          idUpdate={itemIdToUpdate}
        />
        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          fullWidth
          maxWidth="lg"
          sx={{ "& .MuiDialog-paper": { width: "60%" } }}
        >
          <DialogTitle>
            Phân loại khách hàng
            <Typography component="span" sx={{ color: "gray" }}>
              (Thêm mới)
            </Typography>
          </DialogTitle>
          <DialogContent>{/* <CustomerCategorization /> */}</DialogContent>
        </Dialog>
        <Dialog
          open={isOpenModalDeleteMany}
          onClose={() => setIsOpenModalDeleteMany(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Xoá nhóm khách hàng</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Bạn có chắc chắn muốn xoá nhóm khách hàng này không?
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setIsOpenModalDeleteMany(false)} variant="outlined">
              Huỷ
            </Button>
            <Button onClick={confirmDeleteMany} color="error" variant="contained" autoFocus>
              Xoá
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          open={isOpenModalDelete}
          onClose={() => setIsOpenModalDelete(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Xoá nhóm khách hàng</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Bạn có chắc chắn muốn xoá nhóm khách hàng này không?
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setIsOpenModalDelete(false)} variant="outlined">
              Huỷ
            </Button>
            <Button onClick={confirmDelete} color="error" variant="contained" autoFocus>
              Xoá
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </DashboardLayout>
  );
}
