import React, { memo, useState, useCallback } from "react";
import {
  Box,
  Card,
  Typography,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  SelectChangeEvent,
  FormHelperText,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { FormikProps } from "formik";
import { tokens } from "@/src/locales/tokens";
import CustomTextField from "@/src/components/custom-textfield";
import CustomSelect from "@/src/components/custom-select";
import { ProductFormValues } from "../../../../../../types/product/form";
import CloseIcon from "@mui/icons-material/Close";
interface ShippingInformationProps {
  formik: FormikProps<ProductFormValues>;
  warehouses: any[];
}

const localTransportType = [
  { value: "Express", label: "Giao ngay" },
  { value: "Standard", label: "Giao trong ngày" },
];

const ShippingInformation: React.FC<ShippingInformationProps> = memo(({ formik, warehouses }) => {
  const { t } = useTranslation();

  // Local states for number inputs
  const [localWeight, setLocalWeight] = useState(formik.values.itemsWeight);
  const [localLength, setLocalLength] = useState(formik.values.itemsLength);
  const [localWidth, setLocalWidth] = useState(formik.values.itemsWidth);
  const [localHeight, setLocalHeight] = useState(formik.values.itemsHeight);

  // Handle number input changes
  const handleWeightChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const input = e.target;
      const value = input.value.replace(/[^0-9]/g, "");

      if (localWeight === 0 && value !== "0") {
        setLocalWeight(Number(value));
        formik.setFieldValue("itemsWeight", Number(value), true);
        setTimeout(() => {
          input.selectionStart = value.length;
          input.selectionEnd = value.length;
        }, 0);
      } else {
        setLocalWeight(Number(value || 0));
        formik.setFieldValue("itemsWeight", Number(value || 0), true);
      }
    },
    [localWeight, formik]
  );

  const handleLengthChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const input = e.target;
      const value = input.value.replace(/[^0-9]/g, "");

      if (localLength === 0 && value !== "0") {
        setLocalLength(Number(value));
        formik.setFieldValue("itemsLength", Number(value || 0), true);
        setTimeout(() => {
          input.selectionStart = value.length;
          input.selectionEnd = value.length;
        }, 0);
      } else {
        setLocalLength(Number(value || 0));
        formik.setFieldValue("itemsLength", Number(value || 0), true);
      }
    },
    [localLength, formik]
  );

  const handleWidthChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const input = e.target;
      const value = input.value.replace(/[^0-9]/g, "");

      if (localWidth === 0 && value !== "0") {
        setLocalWidth(Number(value));
        formik.setFieldValue("itemsWidth", Number(value), true);
        setTimeout(() => {
          input.selectionStart = value.length;
          input.selectionEnd = value.length;
        }, 0);
      } else {
        setLocalWidth(Number(value || 0));
        formik.setFieldValue("itemsWidth", Number(value || 0), true);
      }
    },
    [localWidth, formik]
  );

  const handleHeightChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const input = e.target;
      const value = input.value.replace(/[^0-9]/g, "");

      if (localHeight === 0 && value !== "0") {
        setLocalHeight(Number(value));
        formik.setFieldValue("itemsHeight", Number(value), true);
        setTimeout(() => {
          input.selectionStart = value.length;
          input.selectionEnd = value.length;
        }, 0);
      } else {
        setLocalHeight(Number(value || 0));
        formik.setFieldValue("itemsHeight", Number(value || 0), true);
      }
    },
    [localHeight, formik]
  );

  // Handle blur events
  const handleWeightBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      formik.setFieldValue("itemsWeight", localWeight);
      formik.handleBlur(e);
    },
    [formik, localWeight]
  );

  const handleLengthBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      formik.setFieldValue("itemsLength", localLength);
      formik.handleBlur(e);
    },
    [formik, localLength]
  );

  const handleWidthBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      formik.setFieldValue("itemsWidth", localWidth);
      formik.handleBlur(e);
    },
    [formik, localWidth]
  );

  const handleHeightBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      formik.setFieldValue("itemsHeight", localHeight);
      formik.handleBlur(e);
    },
    [formik, localHeight]
  );

  // Handle focus to select all text
  const handleFocus = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select();
  }, []);

  // Handle parent category change
  const handleTransportTypeChange = (event: SelectChangeEvent<string[]>) => {
    const transportTypes = event.target.value as string[];
    formik.setFieldValue("transportType", transportTypes);
  };

  const handleDeleteTransportType = (valueToDelete: string) => {
    const newSelected = (formik.values.transportType || []).filter(
      (v: string) => v !== valueToDelete
    );
    formik.setFieldValue("transportType", newSelected);
  };

  // Sync local state when formik values change externally
  React.useEffect(() => {
    setLocalWeight(formik.values.itemsWeight);
  }, [formik.values.itemsWeight]);

  React.useEffect(() => {
    setLocalLength(formik.values.itemsLength);
  }, [formik.values.itemsLength]);

  React.useEffect(() => {
    setLocalWidth(formik.values.itemsWidth);
  }, [formik.values.itemsWidth]);

  React.useEffect(() => {
    setLocalHeight(formik.values.itemsHeight);
  }, [formik.values.itemsHeight]);

  return (
    <Card sx={{ p: 2.5 }}>
      <Typography variant="subtitle1" sx={{ mb: 2.5, fontWeight: 700 }}>
        {t(tokens.contentManagement.product.create.shipping.title)}
      </Typography>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2.5 }}>
        {/* Weight */}
        <Box>
          <Typography
            variant="body2"
            sx={{ display: "flex", fontSize: "16px", fontWeight: 600, mt: 0, mb: 1 }}
          >
            {t(tokens.contentManagement.product.create.shipping.weight)}
          </Typography>
          <CustomTextField
            fullWidth
            type="text"
            name="itemsWeight"
            value={localWeight}
            onChange={handleWeightChange}
            onBlur={handleWeightBlur}
            onFocus={handleFocus}
            error={formik.touched.itemsWeight && Boolean(formik.errors.itemsWeight)}
            helperText={formik.touched.itemsWeight && formik.errors.itemsWeight}
            InputProps={{
              endAdornment: <InputAdornment position="end">g</InputAdornment>,
              inputMode: "numeric",
            }}
            sx={{
              "& .MuiOutlinedInput-root": {
                backgroundColor: "background.paper",
                height: "45px",
              },
            }}
          />
        </Box>

        {/* Dimensions */}
        <Box>
          <Typography
            variant="body2"
            sx={{ display: "flex", fontSize: "16px", fontWeight: 600, mt: 0, mb: 1 }}
          >
            {t(tokens.contentManagement.product.create.shipping.dimensions)}{" "}
          </Typography>
          <Box sx={{ display: "grid", gridTemplateColumns: "repeat(3, 1fr)", gap: 2 }}>
            <CustomTextField
              type="text"
              name="itemsLength"
              label={t(tokens.contentManagement.product.create.shipping.length)}
              value={localLength}
              onChange={handleLengthChange}
              onBlur={handleLengthBlur}
              onFocus={handleFocus}
              error={formik.touched.itemsLength && Boolean(formik.errors.itemsLength)}
              helperText={formik.touched.itemsLength && formik.errors.itemsLength}
              InputProps={{
                endAdornment: <InputAdornment position="end">cm</InputAdornment>,
                inputMode: "numeric",
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  backgroundColor: "background.paper",
                  height: "45px",
                },
              }}
            />
            <CustomTextField
              type="text"
              name="itemsWidth"
              label={t(tokens.contentManagement.product.create.shipping.width)}
              value={localWidth}
              onChange={handleWidthChange}
              onBlur={handleWidthBlur}
              onFocus={handleFocus}
              error={formik.touched.itemsWidth && Boolean(formik.errors.itemsWidth)}
              helperText={formik.touched.itemsWidth && formik.errors.itemsWidth}
              InputProps={{
                endAdornment: <InputAdornment position="end">cm</InputAdornment>,
                inputMode: "numeric",
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  backgroundColor: "background.paper",
                  height: "45px",
                },
              }}
            />
            <CustomTextField
              type="text"
              name="itemsHeight"
              label={t(tokens.contentManagement.product.create.shipping.height)}
              value={localHeight}
              onChange={handleHeightChange}
              onBlur={handleHeightBlur}
              onFocus={handleFocus}
              error={formik.touched.itemsHeight && Boolean(formik.errors.itemsHeight)}
              helperText={formik.touched.itemsHeight && formik.errors.itemsHeight}
              InputProps={{
                endAdornment: <InputAdornment position="end">cm</InputAdornment>,
                inputMode: "numeric",
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  backgroundColor: "background.paper",
                  height: "45px",
                },
              }}
            />
          </Box>
        </Box>

        {/* Warehouse Selection */}
        <Box>
          <Typography
            variant="body2"
            sx={{ display: "flex", fontSize: "16px", fontWeight: 600, mt: 0, mb: 1 }}
          >
            {t(tokens.contentManagement.product.create.shipping.warehouse)}
          </Typography>
          <CustomSelect
            value={formik.values.warehouseId}
            onChange={(e) => formik.setFieldValue("warehouseId", e.target.value)}
            options={warehouses.map((warehouse) => ({
              value: warehouse.warehouseId,
              label: warehouse.warehouseName,
            }))}
            sx={{
              "& .MuiOutlinedInput-root": {
                backgroundColor: "background.paper",
                height: "45px",
              },
            }}
            error={formik.touched.warehouseId && Boolean(formik.errors.warehouseId)}
            helperText={formik.touched.warehouseId && formik.errors.warehouseId}
          />
        </Box>

        <FormControl
          fullWidth
          error={!!(formik.touched.transportType && formik.errors.transportType)}
        >
          <Typography
            variant="body2"
            sx={{ display: "flex", fontSize: "16px", fontWeight: 600, mt: 0, mb: 1 }}
          >
            Hình thức giao hàng
          </Typography>
          <Select
            multiple
            value={formik.values.transportType || []}
            onChange={handleTransportTypeChange}
            // sx={{ paddingTop: 0 }}
            sx={{
              height: "45px",
              backgroundColor: "background.paper",
              ".MuiSelect-select": {
                paddingTop: "8px",
                paddingBottom: "8px",
              },
              "&.MuiInputBase-root": {
                height: "45px",
              },
            }}
            renderValue={(selected) => (
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                {(selected as string[]).map((value) => {
                  const label =
                    localTransportType.find((opt) => opt.value === value)?.label || value;
                  return (
                    <Chip
                      key={value}
                      label={label}
                      onMouseDown={(e) => {
                        e.stopPropagation();
                      }}
                      onDelete={(e) => {
                        e.stopPropagation(); // Chặn click chỉ khi click vào dấu X
                        handleDeleteTransportType(value);
                      }}
                      deleteIcon={<CloseIcon />}
                    />
                  );
                })}
              </Box>
            )}
          >
            {localTransportType.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
          {formik.touched.transportType && formik.errors.transportType && (
            <FormHelperText>{formik.errors.transportType}</FormHelperText>
          )}
        </FormControl>
      </Box>
    </Card>
  );
});

ShippingInformation.displayName = "ShippingInformation";

export default ShippingInformation;
